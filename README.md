# Midas

Midas 是一个专注于苹果退款回传场景的 Go CLI 项目，提供退款数据归集、状态回传与 CDC 同步等离线处理能力。项目强调可观测性与可维护性，支持通过 Debezium + Kafka 的增量同步方案，便于替换旧有的 PHP 扫表脚本。

## 功能亮点
- `sync-refund`：消费 Debezium CDC 事件（`apple_server_notifications`），统一创建/更新退款请求，涵盖原先独立的退款请求与苹果通知同步逻辑。
- `process-refund-requests`：对待处理退款请求计算消费信息并（目前以占位方式）回传 Apple Store Connect。
- Kafka 工具命令：`kafka-produce`、`kafka-consume`、`kafka-multi-consume` 便于调试与排障。

## 快速开始
```bash
# 拉取依赖
go mod download

# 编译 CLI
go build -o bin/midas cmd/cli/main.go

# 使用默认配置启动 refund CDC 同步
./bin/midas sync-refund --config configs/app.toml
```

配置文件模板位于 `configs/app.toml`，核心项包括：
- `mysql.dsn`：统一库连接串（用于再拼接各业务库）。
- `refund`：默认策略、调度延迟、最大处理天数。
- `kafka.subscriptions`：CDC 消费主题与消费组配置（`apple_notifications_cdc` 必须启用）。

## 文档导航
- `doc/refund-sync.md`：退款同步整体流程、指令说明与数据库映射。
- `doc/system-architecture.md`：项目结构、核心组件与数据流概览。
- `doc/runtime-guidelines.md`：Kafka 客户端可调参数与链路追踪 header 规范。
- `doc/legacy-refund-sync.md`：旧版 PHP 命令对照与迁移提示。
- `doc/supervisor.md`：在 Supervisor 中部署常驻任务的参考做法。
- `docker/README.md`：本地 Debezium + Kafka 容器环境使用说明与需求清单。
- `AGENTS.md`：供自动化助手快速了解项目约束的 Prompt。

更多细节、边界条件与扩展建议请参阅 `doc/` 目录。
