<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>CDC 48 小时容量评估 - 简化版</title>
<style>
body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f6f8fa; }
.container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
h1 { color: #24292f; margin: 0 0 20px 0; }
.filters { display: flex; gap: 15px; margin-bottom: 20px; flex-wrap: wrap; align-items: center; }
.filter-item { display: flex; flex-direction: column; gap: 5px; }
.filter-item label { font-weight: 600; font-size: 14px; color: #656d76; }
select { padding: 8px 12px; border: 1px solid #d1d9e0; border-radius: 6px; font-size: 14px; min-width: 150px; }
button { padding: 8px 16px; background: #0969da; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; }
button:hover { background: #0860ca; }
.summary-box { padding: 12px; background: #f6f8fa; border: 1px solid #d1d9e0; border-radius: 6px; font-family: monospace; font-size: 14px; }
table { width: 100%; border-collapse: collapse; margin-top: 20px; }
th, td { padding: 12px; text-align: left; border: 1px solid #d1d9e0; }
th { background: #f6f8fa; font-weight: 600; }
.cell-content { font-size: 12px; line-height: 1.4; }
.cell-content div { margin: 2px 0; }
.totals-row { background: #fff8dc; }
.totals-row th, .totals-row td { font-weight: bold; }
</style>
</head>
<body>
<div class="container">
  <h1>CDC 48 小时容量评估</h1>
  
  <div class="filters">
    <div class="filter-item">
      <label>数据库</label>
      <select id="database-select" multiple size="5">
        <option value="">加载中...</option>
      </select>
    </div>
    <div class="filter-item">
      <label>表名</label>
      <select id="table-select" multiple size="5">
        <option value="">加载中...</option>
      </select>
    </div>
    <div class="filter-item">
      <button onclick="updateTable()">确定</button>
    </div>
    <div class="filter-item">
      <div class="summary-box" id="summary">行数: 0 | 大小(网络): 0 B~0 B | 大小(存储): 0 B</div>
    </div>
  </div>

  <div id="table-container">
    <table id="stats-table">
      <thead></thead>
      <tbody></tbody>
    </table>
  </div>
</div>

<script>
let dataset = {};
let databaseMap = new Map();
let tablesMap = new Map();

// 加载数据
fetch('combined_dataset.json')
  .then(response => response.json())
  .then(data => {
    dataset = data;
    databaseMap = new Map((dataset.databases || []).map(db => [db.name, db]));
    tablesMap = new Map((dataset.tables || []).map(table => [table.name, table]));
    initializeSelects();
    updateTable();
  })
  .catch(error => {
    console.error('加载数据失败:', error);
    document.getElementById('summary').textContent = '数据加载失败';
  });

function initializeSelects() {
  const dbSelect = document.getElementById('database-select');
  const tableSelect = document.getElementById('table-select');
  
  // 清空选项
  dbSelect.innerHTML = '';
  tableSelect.innerHTML = '';
  
  // 添加数据库选项
  (dataset.databases || []).forEach(db => {
    const option = document.createElement('option');
    option.value = db.name;
    option.textContent = db.name;
    option.selected = true; // 默认全选
    dbSelect.appendChild(option);
  });
  
  // 添加表选项
  (dataset.tables || []).forEach(table => {
    const option = document.createElement('option');
    option.value = table.name;
    option.textContent = table.name;
    option.selected = true; // 默认全选
    tableSelect.appendChild(option);
  });
}

function getSelectedValues(selectElement) {
  return Array.from(selectElement.selectedOptions).map(option => option.value);
}

function formatBytes(bytes) {
  if (!bytes) return '0 B';
  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }
  return size.toFixed(2) + ' ' + units[unitIndex];
}

function updateTable() {
  const selectedDbs = getSelectedValues(document.getElementById('database-select'));
  const selectedTables = getSelectedValues(document.getElementById('table-select'));
  
  if (selectedDbs.length === 0 || selectedTables.length === 0) {
    document.getElementById('stats-table').innerHTML = '<thead></thead><tbody><tr><td colspan="100%">请选择至少一个数据库和一张表</td></tr></tbody>';
    document.getElementById('summary').textContent = '行数: 0 | 大小(网络): 0 B~0 B | 大小(存储): 0 B';
    return;
  }
  
  // 计算汇总数据
  let totalRows = 0;
  let totalStorageBytes = 0;
  let totalMinBytes = 0;
  let totalMaxBytes = 0;
  
  const tableData = [];
  
  selectedDbs.forEach(dbName => {
    const db = databaseMap.get(dbName);
    if (!db) return;
    
    const row = { dbName, cells: [] };
    let dbRows = 0;
    let dbStorageBytes = 0;
    let dbMinBytes = 0;
    let dbMaxBytes = 0;
    
    selectedTables.forEach(tableName => {
      const tableData = db.tables && db.tables[tableName];
      if (tableData) {
        const rows = tableData.rows || 0;
        const storageBytes = tableData.storageBytes || 0;
        const minBytes = tableData.debeziumMinBytes || 0;
        const maxBytes = tableData.debeziumMaxBytes || 0;
        
        row.cells.push({
          rows,
          storageBytes,
          minBytes,
          maxBytes,
          exists: true
        });
        
        dbRows += rows;
        dbStorageBytes += storageBytes;
        dbMinBytes += minBytes;
        dbMaxBytes += maxBytes;
      } else {
        row.cells.push({ exists: false });
      }
    });
    
    row.dbTotals = { rows: dbRows, storageBytes: dbStorageBytes, minBytes: dbMinBytes, maxBytes: dbMaxBytes };
    tableData.push(row);
    
    totalRows += dbRows;
    totalStorageBytes += dbStorageBytes;
    totalMinBytes += dbMinBytes;
    totalMaxBytes += dbMaxBytes;
  });
  
  // 更新汇总信息
  document.getElementById('summary').textContent = 
    `行数: ${totalRows.toLocaleString()} | 大小(网络): ${formatBytes(totalMinBytes)}~${formatBytes(totalMaxBytes)} | 大小(存储): ${formatBytes(totalStorageBytes)}`;
  
  // 生成表格
  const table = document.getElementById('stats-table');
  const thead = table.querySelector('thead');
  const tbody = table.querySelector('tbody');
  
  // 生成表头
  thead.innerHTML = '';
  const headerRow = document.createElement('tr');
  headerRow.innerHTML = '<th>数据库</th><th>汇总</th>';
  selectedTables.forEach(tableName => {
    headerRow.innerHTML += `<th>${tableName}</th>`;
  });
  thead.appendChild(headerRow);
  
  // 生成汇总行
  tbody.innerHTML = '';
  const totalsRow = document.createElement('tr');
  totalsRow.className = 'totals-row';
  totalsRow.innerHTML = `
    <th>汇总</th>
    <td><div class="cell-content">
      <div>行数: ${totalRows.toLocaleString()}</div>
      <div>大小(网络): ${formatBytes(totalMinBytes)}~${formatBytes(totalMaxBytes)}</div>
      <div>大小(存储): ${formatBytes(totalStorageBytes)}</div>
    </div></td>
  `;
  
  // 添加表汇总列
  selectedTables.forEach(tableName => {
    let tableRows = 0;
    let tableStorageBytes = 0;
    let tableMinBytes = 0;
    let tableMaxBytes = 0;
    
    tableData.forEach(row => {
      const cellIndex = selectedTables.indexOf(tableName);
      const cell = row.cells[cellIndex];
      if (cell && cell.exists) {
        tableRows += cell.rows;
        tableStorageBytes += cell.storageBytes;
        tableMinBytes += cell.minBytes;
        tableMaxBytes += cell.maxBytes;
      }
    });
    
    totalsRow.innerHTML += `
      <td><div class="cell-content">
        <div>行数: ${tableRows.toLocaleString()}</div>
        <div>大小(网络): ${formatBytes(tableMinBytes)}~${formatBytes(tableMaxBytes)}</div>
        <div>大小(存储): ${formatBytes(tableStorageBytes)}</div>
      </div></td>
    `;
  });
  tbody.appendChild(totalsRow);
  
  // 生成数据行
  tableData.forEach(row => {
    const tr = document.createElement('tr');
    tr.innerHTML = `
      <th>${row.dbName}</th>
      <td><div class="cell-content">
        <div>行数: ${row.dbTotals.rows.toLocaleString()}</div>
        <div>大小(网络): ${formatBytes(row.dbTotals.minBytes)}~${formatBytes(row.dbTotals.maxBytes)}</div>
        <div>大小(存储): ${formatBytes(row.dbTotals.storageBytes)}</div>
      </div></td>
    `;
    
    row.cells.forEach(cell => {
      if (cell.exists) {
        tr.innerHTML += `
          <td><div class="cell-content">
            <div>行数: ${cell.rows.toLocaleString()}</div>
            <div>大小(网络): ${formatBytes(cell.minBytes)}~${formatBytes(cell.maxBytes)}</div>
            <div>大小(存储): ${formatBytes(cell.storageBytes)}</div>
          </div></td>
        `;
      } else {
        tr.innerHTML += '<td><div class="cell-content"><div>-</div></div></td>';
      }
    });
    
    tbody.appendChild(tr);
  });
}
</script>
</body>
</html>
