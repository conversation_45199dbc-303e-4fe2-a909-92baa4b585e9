# Midas 项目 Makefile

.PHONY: build clean test run help deps

# 项目信息
BINARY_NAME=midas
BUILD_DIR=bin
CMD_DIR=cmd/cli

# Go 相关配置
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod

# 构建标志
BUILD_FLAGS=-ldflags="-s -w"

# 默认目标
all: deps build

# 构建二进制文件
build:
	@echo "Building $(BINARY_NAME)..."
	@mkdir -p $(BUILD_DIR)
	$(GOBUILD) $(BUILD_FLAGS) -o $(BUILD_DIR)/$(BINARY_NAME) $(CMD_DIR)/main.go
	@echo "Build completed: $(BUILD_DIR)/$(BINARY_NAME)"

# 清理构建产物
clean:
	@echo "Cleaning..."
	$(GOCLEAN)
	@rm -rf $(BUILD_DIR)
	@echo "Clean completed"

# 运行测试
test:
	@echo "Running tests..."
	$(GOTEST) -v ./...

# 下载依赖
deps:
	@echo "Downloading dependencies..."
	$(GOMOD) download
	$(GOMOD) tidy

# 安装到系统
install: build
	@echo "Installing $(BINARY_NAME) to /usr/local/bin..."
	@sudo cp $(BUILD_DIR)/$(BINARY_NAME) /usr/local/bin/
	@echo "Installation completed"

# 运行帮助
run-help: build
	@$(BUILD_DIR)/$(BINARY_NAME) --help

# 开发环境运行（需要配置数据库）
dev-run: build
	@$(BUILD_DIR)/$(BINARY_NAME) --config=configs/app.toml --verbose

# 格式化代码
fmt:
	@echo "Formatting code..."
	@$(GOCMD) fmt ./...

# 代码检查
vet:
	@echo "Running go vet..."
	@$(GOCMD) vet ./...

# 检查依赖更新
deps-update:
	@echo "Checking for dependency updates..."
	@$(GOCMD) list -u -m all

# 构建 Docker 镜像（如果需要）
docker-build:
	@echo "Building Docker image..."
	@docker build -t $(BINARY_NAME):latest .

# 显示帮助信息
help:
	@echo "Available targets:"
	@echo "  build      - Build the binary"
	@echo "  clean      - Clean build artifacts"  
	@echo "  test       - Run tests"
	@echo "  deps       - Download and tidy dependencies"
	@echo "  install    - Install to /usr/local/bin"
	@echo "  run-help   - Build and show help"
	@echo "  dev-run    - Run with development config"
	@echo "  fmt        - Format code"
	@echo "  vet        - Run go vet"
	@echo "  deps-update - Check for dependency updates"
	@echo "  docker-build - Build Docker image"
	@echo "  help       - Show this help message"
