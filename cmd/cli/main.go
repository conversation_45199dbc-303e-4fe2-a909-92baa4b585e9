package main

import (
	"context"
	"fmt"
	"midas/internal/cli"
	"midas/internal/pkg/ctxx"
	"os"
	"os/signal"
	"syscall"
)

func main() {
	// 创建带有 run_id 的上下文
	ctx := ctxx.WithRunID(context.Background())

	// 处理信号中断
	ctx, cancel := signal.NotifyContext(ctx, os.Interrupt, syscall.SIGTERM)
	defer cancel()

	// 执行根命令
	if err := cli.Execute(ctx); err != nil {
		// 将上下文取消视为正常退出，避免输出 help 信息
		if err == context.Canceled || err == context.DeadlineExceeded {
			os.Exit(0)
		}
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
