# 本地 CDC 方案需求文档（Debezium → Kafka，Kafka-UI 9000）

## 1. 目标与范围

* **目标**：将本机 MySQL 的变更（CDC）通过 **Debezium** 写入 **Kafka**，采用业界通用的 Debezium Envelope（解包后）事件格式；提供 **Kafka-UI** 在 `http://localhost:9000` 观察与调试。
* **保留与策略**：CDC 业务主题默认**保留 3 天**，`cleanup.policy=delete`，用于回放与审计。

  * **Schema 历史主题**与 **Connect 内部主题**保留 **compact** 策略（长期保存元数据）。
* **通用命名**：使用占位符 **`<server>`** 作为 Debezium 的 `server.name`，事件主题命名为：

  ```
  <server>.<db>.<table>
  ```

  Schema 历史主题：`schema-changes.<server>`
* **订阅范围**：默认**全部**（不配置 include 过滤）。后续如需收敛再引入白名单。

> 说明：本文档仅描述容器与配置**需求**、命名标准、验收与非功能要求；**不包含 compose.yml 或 MySQL 人工操作步骤**。

---

## 2. 组件与镜像（本地）

1. **Kafka（bitnami/kafka）**

   * 单机本地运行，推荐 **KRaft 模式**（无需 ZooKeeper）。
   * 对外暴露 `9092`（容器内）与 `localhost:9092`/`localhost:19092`（按实际对外端口规划）。
   * 允许 **Kafka Connect 自动创建主题**（或预先通过命令/API 创建，见 §4）。

2. **Kafka Connect（debezium/connect:2.x）**

   * 提供 Debezium MySQL Source Connector。
   * REST 端口 `8083`（用于注册连接器）。

3. **Kafka-UI（provectuslabs/kafka-ui\:latest）**

   * 对外端口 **9000**。
   * 连接到上面的 Kafka `BOOTSTRAP_SERVERS`。

> 注：本地 **MySQL** 可在宿主或容器中运行。本文默认 **容器化 Connect** 访问宿主 MySQL 时使用 `host.docker.internal:3306` 作为主机名；若 MySQL 也在 Compose 网络内，则使用其**服务名**。

---

## 3. Debezium 连接器配置（需求）

**连接器名称**：`mysql-apps-cdc`

**配置要求（JSON 模板）**：

```json
{
  "name": "mysql-apps-cdc",
  "config": {
    "connector.class": "io.debezium.connector.mysql.MySqlConnector",

    // ---- MySQL 连接（订阅范围默认全部）----
    "database.hostname": "127.0.0.1",             // 本机容器MySQL地址
    "database.port": "3306",
    "database.user": "debezium",
    "database.password": "debezium",

    // Debezium 标识
    "database.server.id": "5401",
    "database.server.name": "apps",               // 作为主题前缀的一部分

    // 初次快照 + 增量
    "snapshot.mode": "initial",
    "include.schema.changes": "false",            // 不输出 DDL 事件到业务流

    // ---- Schema 历史（Kafka 存储）----
    "schema.history.internal.kafka.bootstrap.servers": "kafka:9092",
    "schema.history.internal.kafka.topic": "schema-changes.apps",

    // ---- 事件解包（业界常用）+ Key路由 ----
    "transforms": "unwrap,keyRoute",
    "transforms.unwrap.type": "io.debezium.transforms.ExtractNewRecordState",
    "transforms.unwrap.drop.tombstones": "true",
    "transforms.keyRoute.type": "org.apache.kafka.connect.transforms.ValueToKey",
    "transforms.keyRoute.fields": "source.db",

    // ---- 主题自动创建（仅对由该连接器创建的主题生效）----
    "topic.creation.enable": "true",

    // 业务 CDC 主题（默认规则）
    "topic.creation.default.partitions": "12",
    "topic.creation.default.replication.factor": "1",
    "topic.creation.default.cleanup.policy": "delete",
    "topic.creation.default.retention.ms": "259200000",

    // Schema 历史主题（history 规则）
    "topic.creation.history.include": "schema-changes.*",
    "topic.creation.history.partitions": "1",
    "topic.creation.history.replication.factor": "1",
    "topic.creation.history.cleanup.policy": "compact"
  }
}
```

**输出主题示例**：

```
apps.second_number_2.apple_server_notifications
apps.auth_app_lt.apple_server_notifications
...
schema-changes.apps                    // compact
connect-configs / connect-offsets / connect-status // Connect 内部主题，compact
```

> 备注：
>
> * 本地单 broker 下 `replication.factor` 只能为 `1`。如扩展到多 broker，请在部署流程中提升 `replication.factor` 与 `min.insync.replicas`。
> * 默认**不配置** `database.include.list` / `table.include.list`，即订阅全部；后续可按需加白名单。

---

## 4. Kafka 主题与保留策略（需求）

* **业务 CDC 主题**（由连接器自动创建）：

  * `cleanup.policy=delete`
  * `retention.ms=259200000`（3 天）
  * `partitions=6`
* **Schema 历史主题**：`schema-changes.apps` → `cleanup.policy=compact`
* **Connect 内部主题**：`connect-configs` / `connect-offsets` / `connect-status` → `compact`

> 如 Kafka 集群关闭了“自动创建主题”，则需在自动化流程中**预建**上述主题并应用等效配置；否则允许 Connect 侧 `topic.creation.*` 创建。

---

## 5. Kafka-UI（需求）

* **镜像**：`provectuslabs/kafka-ui:latest`
* **端口**：对外暴露 **9000**
* **最小环境**：

  * `KAFKA_CLUSTERS_0_NAME=local`
  * `KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS=kafka:9092`
  * `SERVER_PORT=9000`
* **可观测项**：主题列表、消息浏览、消费组滞后（lag），支持针对前缀 `<server>.` 的过滤查看。

---

## 6. 网络与地址（本地约束）

* **Connect 容器 → 宿主 MySQL**：使用 `host.docker.internal:3306`。
* **若 MySQL 也容器化**：Connect 同网络下使用 MySQL **服务名**与容器端口（通常 `3306`）。
* **Kafka-UI → Kafka**：容器内名称 `kafka:9092`；宿主工具（如 `kcat`）使用 `localhost:9092` 或实际映射端口。

---

## 7. 验收标准（Acceptance Criteria）

1. **主题与命名**

   * Debezium 注册成功后，出现 `schema-changes.apps`（compact）与若干 `apps.<db>.<table>` 主题。
2. **保留策略**

   * 业务 CDC 主题具备 `cleanup.policy=delete`，`retention.ms=259200000`。
   * `schema-changes.apps`、`connect-*` 为 `compact`。
3. **端到端验证**

   * 对任一表执行 `INSERT/UPDATE/DELETE`，在 `apps.<db>.<table>` 主题可消费到**解包后的** JSON 事件（含 `op: c/u/d` 与 `after/before`）。
   * Kafka-UI 在 `http://localhost:9000` 可查看消息、主题配置与消费组状态。
4. **回放验证（3 天内）**

   * 可通过消费者 `seek`/按时间回拨方式重放近 3 天内的消息。
5. **稳定性**

   * 重启 Connect 后，从上次 offset 继续采集；MySQL 短暂断连恢复后自动续跑。

---

## 8. 交付物与接口

* **交付物**

  * 连接器注册 JSON 模板（见 §3）。
  * Kafka-UI 环境变量清单（见 §5）。
* **接口**

  * **Kafka Connect REST**：`POST /connectors` 注册连接器；`GET /connectors/<name>/status` 查询状态。
  * **Kafka-UI**：`GET http://localhost:9000` Web 访问。
