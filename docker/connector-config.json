{"name": "mysql-apps-cdc", "config": {"connector.class": "io.debezium.connector.mysql.MySqlConnector", "database.hostname": "host.docker.internal", "database.port": "3306", "database.user": "debezium", "database.password": "debezium", "database.server.id": "5401", "database.server.name": "apps", "topic.prefix": "apps", "snapshot.mode": "initial", "include.schema.changes": "false", "schema.history.internal.kafka.bootstrap.servers": "kafka:9092", "schema.history.internal.kafka.topic": "schema-changes.apps", "transforms": "addSourceName,addRunMode,addSourceIP,addCaller", "transforms.addSourceName.type": "org.apache.kafka.connect.transforms.InsertHeader", "transforms.addSourceName.header": "X-Passthrough-Source-Name", "transforms.addSourceName.value.literal": "debezium-cdc", "transforms.addRunMode.type": "org.apache.kafka.connect.transforms.InsertHeader", "transforms.addRunMode.header": "X-Passthrough-Run-Mode", "transforms.addRunMode.value.literal": "prod", "transforms.addSourceIP.type": "org.apache.kafka.connect.transforms.InsertHeader", "transforms.addSourceIP.header": "X-Passthrough-Source-Ip", "transforms.addSourceIP.value.literal": "127.0.0.1", "transforms.addCaller.type": "org.apache.kafka.connect.transforms.InsertHeader", "transforms.addCaller.header": "<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>", "transforms.addCaller.value.literal": "mysql-binlog", "topic.creation.enable": "true", "topic.creation.default.partitions": "12", "topic.creation.default.replication.factor": "1", "topic.creation.default.cleanup.policy": "delete", "topic.creation.default.retention.ms": "259200000", "topic.creation.history.include": "schema-changes.*", "topic.creation.history.partitions": "1", "topic.creation.history.replication.factor": "1", "topic.creation.history.cleanup.policy": "compact"}}