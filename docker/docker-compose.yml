version: "3.9"

services:
  kafka:
    image: bitnami/kafka:latest
    container_name: midas-kafka
    ports:
      - "9092:9092"
      - "19092:19092"
    environment:
      # KRaft mode configuration
      - KAFKA_ENABLE_KRAFT=yes
      - KAFKA_CFG_PROCESS_ROLES=broker,controller
      - K<PERSON>KA_CFG_NODE_ID=1
      - KAFKA_CFG_CONTROLLER_QUORUM_VOTERS=1@kafka:9093
      - KAFKA_CFG_CONTROLLER_LISTENER_NAMES=CONTROLLER
      - KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP=PLAINTEXT:PLAINTEXT,CONTROLLER:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      - KAFKA_CFG_LISTENERS=PLAINTEXT://:9092,CONTROLLER://:9093,PLAINTEXT_HOST://:19092
      - KAFKA_CFG_ADVERTISED_LISTENERS=PLAINTEXT://kafka:9092,PLAINTEXT_HOST://localhost:19092
      - <PERSON><PERSON><PERSON>_CFG_INTER_BROKER_LISTENER_NAME=PLAINTEXT
      # Allow auto creation of topics for Kafka Connect
      - KAFKA_CFG_AUTO_CREATE_TOPICS_ENABLE=true
      - ALLOW_PLAINTEXT_LISTENER=yes
      # Default topic configurations
      - KAFKA_CFG_NUM_PARTITIONS=12
      - KAFKA_CFG_DEFAULT_REPLICATION_FACTOR=1
      - KAFKA_CFG_MIN_INSYNC_REPLICAS=1
    volumes:
      - /Users/<USER>/Desktop/data/kafka:/bitnami/kafka
    networks:
      - midas-net
    restart: unless-stopped

  kafka-connect:
    image: debezium/connect:3.0.0.Final
    container_name: midas-kafka-connect
    depends_on:
      - kafka
    ports:
      - "8083:8083"
    environment:
      - BOOTSTRAP_SERVERS=kafka:9092
      - GROUP_ID=1
      - CONFIG_STORAGE_TOPIC=connect-configs
      - OFFSET_STORAGE_TOPIC=connect-offsets
      - STATUS_STORAGE_TOPIC=connect-status
      # Internal topics configuration
      - CONFIG_STORAGE_REPLICATION_FACTOR=1
      - OFFSET_STORAGE_REPLICATION_FACTOR=1
      - STATUS_STORAGE_REPLICATION_FACTOR=1
      # Connect worker settings
      - CONNECT_KEY_CONVERTER=org.apache.kafka.connect.json.JsonConverter
      - CONNECT_VALUE_CONVERTER=org.apache.kafka.connect.json.JsonConverter
      - CONNECT_KEY_CONVERTER_SCHEMAS_ENABLE=true
      - CONNECT_VALUE_CONVERTER_SCHEMAS_ENABLE=true
      - CONNECT_INTERNAL_KEY_CONVERTER=org.apache.kafka.connect.json.JsonConverter
      - CONNECT_INTERNAL_VALUE_CONVERTER=org.apache.kafka.connect.json.JsonConverter
      - CONNECT_INTERNAL_KEY_CONVERTER_SCHEMAS_ENABLE=true
      - CONNECT_INTERNAL_VALUE_CONVERTER_SCHEMAS_ENABLE=true
      - CONNECT_REST_ADVERTISED_HOST_NAME=kafka-connect
      - CONNECT_PLUGIN_PATH=/kafka/connect
    volumes:
      - /Users/<USER>/Desktop/data/kafka-connect:/kafka/connect/data
    networks:
      - midas-net
    restart: unless-stopped

  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: midas-kafka-ui
    depends_on:
      - kafka
    ports:
      - "9000:9000"
    environment:
      - KAFKA_CLUSTERS_0_NAME=local
      - KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS=kafka:9092
      - KAFKA_CLUSTERS_0_KAFKACONNECT_0_NAME=main
      - KAFKA_CLUSTERS_0_KAFKACONNECT_0_ADDRESS=http://kafka-connect:8083
      - SERVER_PORT=9000
    networks:
      - midas-net
    restart: unless-stopped

networks:
  midas-net:
    driver: bridge


