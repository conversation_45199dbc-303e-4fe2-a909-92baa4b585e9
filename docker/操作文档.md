# Midas CDC 本地环境操作文档

## 1. 环境概览

本环境基于 Docker Compose 部署，包含以下组件：

- **Kafka** (bitnami/kafka:latest): KRaft 模式单节点集群
- **Kafka Connect** (debezium/connect:3.0.0.Final): Debezium CDC 连接器
- **Kafka UI** (provectuslabs/kafka-ui:latest): Web 界面管理工具

### 服务端口映射

| 服务 | 容器端口 | 宿主端口 | 用途 |
|------|----------|----------|------|
| Kafka | 9092 | 9092 | 内部通信 |
| Kafka | 19092 | 19092 | 外部客户端连接 |
| Kafka Connect | 8083 | 8083 | REST API |
| Kafka UI | 9000 | 9000 | Web 管理界面 |

### 数据存储目录

- Kafka 数据: `/Users/<USER>/Desktop/data/kafka`
- Kafka Connect 数据: `/Users/<USER>/Desktop/data/kafka-connect`

---

## 2. 环境管理

### 2.1 启动环境

```bash
cd /Users/<USER>/Desktop/code/work/midas/docker

# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f
```

### 2.2 停止环境

```bash
# 停止所有服务
docker-compose down

# 停止并删除数据卷（谨慎使用）
docker-compose down -v
```

### 2.3 重启单个服务

```bash
# 重启 Kafka
docker-compose restart kafka

# 重启 Kafka Connect
docker-compose restart kafka-connect

# 重启 Kafka UI
docker-compose restart kafka-ui
```

### 2.4 查看服务日志

```bash
# 查看 Kafka 日志
docker-compose logs -f kafka

# 查看 Kafka Connect 日志
docker-compose logs -f kafka-connect

# 查看 Kafka UI 日志
docker-compose logs -f kafka-ui
```

---

## 3. Debezium 连接器管理

### 3.1 查看连接器状态

```bash
# 查看所有连接器
curl -s http://localhost:8083/connectors | jq

# 查看特定连接器状态
curl -s http://localhost:8083/connectors/mysql-apps-cdc/status | jq

# 查看连接器配置
curl -s http://localhost:8083/connectors/mysql-apps-cdc/config | jq
```

### 3.2 创建 MySQL CDC 连接器

创建连接器配置文件 `connector-config.json`:

```json
{
  "name": "mysql-apps-cdc",
  "config": {
    "connector.class": "io.debezium.connector.mysql.MySqlConnector",
    
    "database.hostname": "127.0.0.1",
    "database.port": "3306",
    "database.user": "debezium",
    "database.password": "debezium",
    
    "database.server.id": "5401",
    "database.server.name": "apps",
    
    "snapshot.mode": "initial",
    "include.schema.changes": "false",
    
    "schema.history.internal.kafka.bootstrap.servers": "kafka:9092",
    "schema.history.internal.kafka.topic": "schema-changes.apps",
    
    "transforms": "unwrap,keyRoute",
    "transforms.unwrap.type": "io.debezium.transforms.ExtractNewRecordState",
    "transforms.unwrap.drop.tombstones": "true",
    "transforms.keyRoute.type": "org.apache.kafka.connect.transforms.ValueToKey",
    "transforms.keyRoute.fields": "source.db",
    
    "topic.creation.enable": "true",
    "topic.creation.default.partitions": "12",
    "topic.creation.default.replication.factor": "1",
    "topic.creation.default.cleanup.policy": "delete",
    "topic.creation.default.retention.ms": "259200000",
    
    "topic.creation.history.include": "schema-changes.*",
    "topic.creation.history.partitions": "1",
    "topic.creation.history.replication.factor": "1",
    "topic.creation.history.cleanup.policy": "compact"
  }
}
```

注册连接器：

```bash
curl -i -X POST -H "Accept:application/json" -H "Content-Type:application/json" \
  http://localhost:8083/connectors/ -d @connector-config.json
```

### 3.3 管理连接器

```bash
# 暂停连接器
curl -i -X PUT http://localhost:8083/connectors/mysql-apps-cdc/pause

# 恢复连接器
curl -i -X PUT http://localhost:8083/connectors/mysql-apps-cdc/resume

# 重启连接器
curl -i -X POST http://localhost:8083/connectors/mysql-apps-cdc/restart

# 删除连接器
curl -i -X DELETE http://localhost:8083/connectors/mysql-apps-cdc
```

### 3.4 更新连接器配置

```bash
# 更新连接器配置
curl -i -X PUT -H "Accept:application/json" -H "Content-Type:application/json" \
  http://localhost:8083/connectors/mysql-apps-cdc/config -d @new-connector-config.json
```

---

## 4. Kafka 主题管理

### 4.1 使用 Docker 命令管理主题

```bash
# 进入 Kafka 容器
docker exec -it midas-kafka bash

# 列出所有主题
kafka-topics.sh --bootstrap-server localhost:9092 --list

# 查看主题详情
kafka-topics.sh --bootstrap-server localhost:9092 --topic apps.<db>.<table> --describe

# 创建主题
kafka-topics.sh --bootstrap-server localhost:9092 --create --topic test-topic \
  --partitions 12 --replication-factor 1

# 删除主题
kafka-topics.sh --bootstrap-server localhost:9092 --delete --topic test-topic
```

### 4.2 修改主题配置

```bash
# 修改主题保留时间（3天）
kafka-configs.sh --bootstrap-server localhost:9092 --entity-type topics \
  --entity-name apps.<db>.<table> --alter \
  --add-config retention.ms=259200000

# 修改主题清理策略
kafka-configs.sh --bootstrap-server localhost:9092 --entity-type topics \
  --entity-name schema-changes.apps --alter \
  --add-config cleanup.policy=compact

# 查看主题配置
kafka-configs.sh --bootstrap-server localhost:9092 --entity-type topics \
  --entity-name apps.<db>.<table> --describe
```

### 4.3 消费消息

```bash
# 从最新位置开始消费
kafka-console-consumer.sh --bootstrap-server localhost:9092 \
  --topic apps.<db>.<table> --from-beginning

# 消费指定分区
kafka-console-consumer.sh --bootstrap-server localhost:9092 \
  --topic apps.<db>.<table> --partition 0 --offset earliest

# 消费消息并显示键值
kafka-console-consumer.sh --bootstrap-server localhost:9092 \
  --topic apps.<db>.<table> --property print.key=true \
  --property key.separator=" : " --from-beginning
```

---

## 5. 监控与故障排查

### 5.1 使用 Kafka UI 监控

访问 `http://localhost:9000` 查看：

- 主题列表和配置
- 消息内容浏览
- 消费组状态和滞后
- 连接器状态和配置

### 5.2 健康检查

```bash
# 检查 Kafka 连通性
docker exec midas-kafka kafka-broker-api-versions.sh --bootstrap-server localhost:9092

# 检查 Kafka Connect 健康状态
curl -s http://localhost:8083/ | jq

# 检查连接器插件
curl -s http://localhost:8083/connector-plugins | jq
```

### 5.3 常见问题排查

#### 连接器无法连接 MySQL

```bash
# 检查网络连通性
docker exec midas-kafka-connect ping host.docker.internal

# 检查 MySQL 用户权限
# 在 MySQL 中执行：
# GRANT SELECT, RELOAD, SHOW DATABASES, REPLICATION SLAVE, REPLICATION CLIENT ON *.* TO 'debezium'@'%';
# FLUSH PRIVILEGES;
```

#### 主题创建失败

```bash
# 检查 Kafka 日志
docker-compose logs kafka

# 手动创建主题
docker exec midas-kafka kafka-topics.sh --bootstrap-server localhost:9092 \
  --create --topic schema-changes.<server> --partitions 1 --replication-factor 1 \
  --config cleanup.policy=compact
```

#### 连接器任务失败

```bash
# 查看任务状态
curl -s http://localhost:8083/connectors/mysql-apps-cdc/tasks | jq

# 重启失败的任务
curl -i -X POST http://localhost:8083/connectors/mysql-apps-cdc/tasks/0/restart
```

---

## 6. 数据备份与恢复

### 6.1 数据目录备份

```bash
# 停止服务
docker-compose down

# 备份数据目录
sudo cp -r /Users/<USER>/Desktop/data /Users/<USER>/Desktop/data.backup.$(date +%Y%m%d_%H%M%S)

# 重启服务
docker-compose up -d
```

### 6.2 导出主题数据

```bash
# 导出主题所有消息
docker exec midas-kafka kafka-console-consumer.sh --bootstrap-server localhost:9092 \
  --topic apps.<db>.<table> --from-beginning \
  --timeout-ms 10000 > topic_backup.json

# 导出连接器配置
curl -s http://localhost:8083/connectors/mysql-apps-cdc/config > connector_config_backup.json
```

### 6.3 恢复连接器

```bash
# 从备份恢复连接器配置
curl -i -X POST -H "Accept:application/json" -H "Content-Type:application/json" \
  http://localhost:8083/connectors/ -d @connector_config_backup.json
```

---

## 7. 性能优化

### 7.1 调整 Kafka 配置

在 `docker-compose.yml` 中调整以下参数：

```yaml
environment:
  # 增加内存配置
  - KAFKA_HEAP_OPTS=-Xmx2G -Xms2G
  # 调整日志段大小
  - KAFKA_CFG_LOG_SEGMENT_BYTES=1073741824
  # 调整刷盘间隔
  - KAFKA_CFG_LOG_FLUSH_INTERVAL_MESSAGES=10000
```

### 7.2 调整连接器配置

```json
{
  "config": {
    // 增加最大批次大小
    "max.batch.size": "2048",
    // 调整轮询间隔
    "poll.interval.ms": "1000",
    // 增加队列容量
    "max.queue.size": "8192"
  }
}
```

### 7.3 监控性能指标

```bash
# 查看主题消息速率
docker exec midas-kafka kafka-run-class.sh kafka.tools.ConsumerPerformance \
  --bootstrap-server localhost:9092 --topic apps.<db>.<table> \
  --messages 1000 --threads 1

# 查看连接器指标
curl -s http://localhost:8083/connectors/mysql-apps-cdc/status | \
  jq '.tasks[].trace'
```

---

## 8. 安全注意事项

### 8.1 网络安全

- 确保只有必要的端口对外暴露
- 在生产环境中使用防火墙限制访问
- 考虑使用 VPN 或内网访问

### 8.2 数据安全

- 定期备份重要数据
- 使用强密码连接 MySQL
- 考虑启用 SSL/TLS 加密

### 8.3 访问控制

- 限制 Kafka Connect REST API 的访问
- 定期轮换数据库密码
- 监控异常访问模式

---

## 9. 日常维护清单

### 每日检查

- [ ] 检查所有服务状态 `docker-compose ps`
- [ ] 查看错误日志 `docker-compose logs --tail=100`
- [ ] 检查连接器状态 `curl http://localhost:8083/connectors/mysql-<server>-cdc/status`
- [ ] 查看主题消息积压情况（通过 Kafka UI）

### 每周维护

- [ ] 清理过期日志文件
- [ ] 检查磁盘空间使用情况
- [ ] 备份重要配置文件
- [ ] 更新服务状态文档

### 每月维护

- [ ] 完整数据备份
- [ ] 更新容器镜像版本
- [ ] 性能指标分析
- [ ] 安全审计检查

---

## 10. 紧急情况处理

### 服务完全不可用

1. 检查 Docker 服务状态
2. 检查磁盘空间
3. 查看系统资源使用情况
4. 重启 Docker Compose 服务
5. 从备份恢复数据（如需要）

### 数据丢失

1. 停止所有写入操作
2. 检查数据目录完整性
3. 从最近备份恢复
4. 重新配置连接器
5. 验证数据一致性

### 联系信息

- 系统管理员：[联系信息]
- 紧急联系电话：[电话号码]
- 文档维护：[维护人员]

---

*文档版本：1.0*  
*最后更新：2025-09-18*
