# Docker 环境手册

本目录提供本地 CDC 基础设施（Kafka + Debezium + Kafka-UI）的编排文件及操作说明，适合调试 `midas sync-refund` 等命令。

## 1. 环境组成
| 服务 | 镜像 | 端口 | 说明 |
| ---- | ---- | ---- | ---- |
| Kafka | `bitnami/kafka:latest` | `9092`（内部） / `19092`（宿主） | KRaft 模式单节点，允许自动创建主题。|
| Kafka Connect | `debezium/connect:3.0.0.Final` | `8083` | Debezium 连接器运行时。|
| Kafka UI | `provectuslabs/kafka-ui:latest` | `9000` | Web 调试界面，预配置了 Kafka 与 Connect。|

数据卷默认挂载在宿主的 `/Users/<USER>/Desktop/data/{kafka,kafka-connect}`，可在 `docker-compose.yml` 中调整。

## 2. 需求与约束
- 目标：将本机 MySQL 的 CDC 事件（Debezium Envelope 解包后）写入 Kafka，主题命名规范 `apps.<db>.<table>`。
- 业务主题保留：`cleanup.policy=delete`，`retention.ms=259200000`（3 天）。
- Schema 及 Connect 内部主题：`cleanup.policy=compact`。
- 默认不限制 MySQL 库/表，后续可在连接器增加白名单。

示例连接器模板位于 `connector-config.json`，关键字段包括：
```json
{
  "name": "mysql-apps-cdc",
  "config": {
    "database.hostname": "127.0.0.1",
    "database.user": "debezium",
    "database.password": "debezium",
    "database.server.name": "apps",
    "snapshot.mode": "initial",
    "transforms": "unwrap,keyRoute",
    "transforms.unwrap.type": "io.debezium.transforms.ExtractNewRecordState",
    "topic.creation.default.retention.ms": "259200000"
  }
}
```

## 3. 常用命令
```bash
# 启动/停止
cd docker
docker-compose up -d
docker-compose down          # 保留数据
docker-compose down -v       # 连同数据卷清理

# 查看状态与日志
docker-compose ps
docker-compose logs -f kafka
```

### 3.1 Kafka Connect 操作
```bash
# 查看已注册连接器
curl -s http://localhost:8083/connectors | jq

# 注册连接器
docker-compose exec kafka-connect bash -c \
  "curl -i -X POST -H 'Content-Type:application/json' \
  http://localhost:8083/connectors -d @/connect/connector-config.json"

# 查询连接器状态
docker-compose exec kafka-connect curl -s \
  http://localhost:8083/connectors/mysql-apps-cdc/status | jq
```

### 3.2 Kafka 主题与保留策略
- CDC 业务主题：`cleanup.policy=delete`，`retention.ms=259200000`。
- Schema 历史：`schema-changes.apps`，`cleanup.policy=compact`。
- Connect 内部：`connect-configs` / `connect-offsets` / `connect-status`，`cleanup.policy=compact`。

若禁用自动建主题，可使用以下命令预建：
```bash
docker exec midas-kafka kafka-topics.sh --bootstrap-server localhost:9092 \
  --create --topic apps.second_number_2.apple_server_notifications \
  --partitions 12 --replication-factor 1 \
  --config retention.ms=259200000 --config cleanup.policy=delete
```

## 4. 验收清单
1. 注册 `mysql-apps-cdc` 后，Kafka-UI 中应出现 `apps.<db>.<table>` 与 `schema-changes.apps`。
2. 对业务库执行 `INSERT/UPDATE/DELETE`，`apps.<db>.<table>` 主题可消费到 `after` 字段被解包的 JSON。
3. Kafka-UI 显示消费组延迟，`sync-refund` 消费后 offset 应平稳推进。
4. Connect 重启后可自动续跑，短暂断连恢复后不会丢失数据。

## 5. 日常维护
- **每日**：`docker-compose ps` 检查服务，关注 `docker-compose logs --tail=100` 是否有错误。
- **每周**：清理老旧日志，确认磁盘空间并备份 `connector-config.json`。
- **每月**：刷新镜像、核对保留策略是否符合需求、执行安全审计。

## 6. 安全提示
- 生产环境建议限制对外暴露端口，或通过 VPN / VPC 访问。
- 避免将真实凭据写入 `docker-compose.yml`；优先使用 `.env` 注入敏感变量。
- `host.docker.internal` 在 Linux 可能不可用，需根据实际环境改为网桥 IP 或容器服务名。

## 7. 贡献者指引
- Compose 文件使用 2 空格缩进，服务名采用 kebab-case。
- JSON 配置保持 2 空格缩进与明确注释，如需新增字段请补充文档。
- 修改后运行 `docker compose config -q` 校验语法；提交 PR 时附上关键命令与运行截图。
- 若变更挂载目录，避免写死个人路径，优先引入 `.env` 变量。
