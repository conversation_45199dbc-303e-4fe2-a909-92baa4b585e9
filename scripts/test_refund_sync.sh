#!/bin/bash

# 退款同步功能测试脚本
# 用于验证基于 binlog 订阅的退款同步功能

set -e

echo "=== 退款同步功能测试脚本 ==="
echo

# 配置变量
MYSQL_HOST="localhost"
MYSQL_USER="root"
MYSQL_PASS="root@123"
MIDAS_BIN="./bin/midas"
WEB_DIR="/Users/<USER>/Desktop/code/work/web-secondnumber2"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查 MySQL 连接
    if ! mysql -h "$MYSQL_HOST" -u "$MYSQL_USER" -p"$MYSQL_PASS" -e "SELECT 1;" >/dev/null 2>&1; then
        log_error "MySQL 连接失败，请检查连接配置"
        exit 1
    fi
    log_success "MySQL 连接正常"
    
    # 检查 midas 二进制文件
    if [ ! -f "$MIDAS_BIN" ]; then
        log_error "midas 二进制文件不存在，请先编译项目"
        exit 1
    fi
    log_success "midas 二进制文件存在"
    
    # 检查 web 目录
    if [ ! -d "$WEB_DIR" ]; then
        log_warning "web 目录不存在: $WEB_DIR"
        log_warning "将跳过 PHP 通知拉取步骤"
    else
        log_success "web 目录存在"
    fi
}

# 创建测试数据
create_test_data() {
    log_info "创建测试数据..."
    
    if mysql -h "$MYSQL_HOST" -u "$MYSQL_USER" -p"$MYSQL_PASS" < scripts/create_test_data.sql; then
        log_success "测试数据创建成功"
    else
        log_error "测试数据创建失败"
        exit 1
    fi
}

# 验证测试数据
verify_test_data() {
    log_info "验证测试数据..."
    
    # 检查订单数据
    ORDER_COUNT=$(mysql -h "$MYSQL_HOST" -u "$MYSQL_USER" -p"$MYSQL_PASS" -sN -e "SELECT COUNT(*) FROM second_number_2.all_app_orders WHERE transaction_id LIKE 'TEST_%';")
    log_info "测试订单数量: $ORDER_COUNT"
    
    # 检查通知数据
    NOTIFICATION_COUNT=$(mysql -h "$MYSQL_HOST" -u "$MYSQL_USER" -p"$MYSQL_PASS" -sN -e "SELECT COUNT(*) FROM second_number_2.apple_server_notifications WHERE original_transaction_id LIKE 'TEST_%';")
    log_info "测试通知数量: $NOTIFICATION_COUNT"
    
    # 检查App配置
    APP_CONFIG_COUNT=$(mysql -h "$MYSQL_HOST" -u "$MYSQL_USER" -p"$MYSQL_PASS" -sN -e "SELECT COUNT(*) FROM cohort.app_config WHERE adam_id = 1234567890;")
    log_info "测试App配置数量: $APP_CONFIG_COUNT"
    
    if [ "$ORDER_COUNT" -gt 0 ] && [ "$NOTIFICATION_COUNT" -gt 0 ] && [ "$CONSUMPTION_COUNT" -gt 0 ] && [ "$APP_CONFIG_COUNT" -gt 0 ]; then
        log_success "测试数据验证通过"
    else
        log_error "测试数据验证失败"
        exit 1
    fi
}

# 启动退款同步服务（后台运行）
start_sync_service() {
    log_info "启动退款同步服务..."
    
    # 检查是否已有进程在运行
    if pgrep -f "midas sync-refund" > /dev/null; then
        log_warning "退款同步服务已在运行，先停止现有进程"
        pkill -f "midas sync-refund" || true
        sleep 2
    fi
    
    # 启动服务（后台运行）
    nohup "$MIDAS_BIN" sync-refund > logs/sync-refund-test.log 2>&1 &
    SYNC_PID=$!
    
    log_info "退款同步服务已启动，PID: $SYNC_PID"
    log_info "日志文件: logs/sync-refund-test.log"
    
    # 等待服务启动
    sleep 3
    
    # 检查服务是否正常运行
    if kill -0 "$SYNC_PID" 2>/dev/null; then
        log_success "退款同步服务运行正常"
        echo "$SYNC_PID" > /tmp/midas_sync_refund.pid
    else
        log_error "退款同步服务启动失败"
        exit 1
    fi
}

# 触发通知拉取（如果 web 目录存在）
trigger_notifications() {
    if [ -d "$WEB_DIR" ]; then
        log_info "触发通知拉取..."
        
        cd "$WEB_DIR"
        if php artisan process-notification-history --window 1h --batch-size 10; then
            log_success "通知拉取完成"
        else
            log_warning "通知拉取失败，但继续测试"
        fi
        cd - > /dev/null
    else
        log_warning "跳过通知拉取步骤（web 目录不存在）"
    fi
}

# 等待处理完成
wait_for_processing() {
    log_info "等待数据处理完成..."
    
    # 等待一段时间让数据处理完成
    sleep 10
    
    log_success "等待完成"
}

# 验证处理结果
verify_results() {
    log_info "验证处理结果..."
    
    # 检查是否创建了退款请求
    REFUND_REQUEST_COUNT=$(mysql -h "$MYSQL_HOST" -u "$MYSQL_USER" -p"$MYSQL_PASS" -sN -e "SELECT COUNT(*) FROM cohort.refund_requests WHERE transaction_id LIKE 'TEST_%';")
    log_info "创建的退款请求数量: $REFUND_REQUEST_COUNT"
    
    # 检查退款状态更新
    REFUND_STATUS_COUNT=$(mysql -h "$MYSQL_HOST" -u "$MYSQL_USER" -p"$MYSQL_PASS" -sN -e "SELECT COUNT(*) FROM cohort.refund_requests WHERE transaction_id LIKE 'TEST_%' AND refund_result IS NOT NULL;")
    log_info "已更新退款状态的请求数量: $REFUND_STATUS_COUNT"
    
    if [ "$REFUND_REQUEST_COUNT" -gt 0 ]; then
        log_success "退款请求创建成功"
    else
        log_warning "未创建退款请求"
    fi
    
    if [ "$SYNCED_COUNT" -gt 0 ]; then
        log_success "消费请求同步成功"
    else
        log_warning "消费请求未同步"
    fi
}

# 停止同步服务
stop_sync_service() {
    log_info "停止退款同步服务..."
    
    if [ -f /tmp/midas_sync_refund.pid ]; then
        SYNC_PID=$(cat /tmp/midas_sync_refund.pid)
        if kill -0 "$SYNC_PID" 2>/dev/null; then
            kill "$SYNC_PID"
            log_success "退款同步服务已停止"
        fi
        rm -f /tmp/midas_sync_refund.pid
    fi
    
    # 确保所有相关进程都停止
    pkill -f "midas sync-refund" || true
}

# 清理测试数据
cleanup_test_data() {
    log_info "清理测试数据..."
    
    mysql -h "$MYSQL_HOST" -u "$MYSQL_USER" -p"$MYSQL_PASS" -e "
        DELETE FROM second_number_2.all_app_orders WHERE transaction_id LIKE 'TEST_%';
        DELETE FROM second_number_2.apple_server_notifications WHERE original_transaction_id LIKE 'TEST_%';
        DELETE FROM cohort.refund_requests WHERE transaction_id LIKE 'TEST_%';
    "
    
    log_success "测试数据清理完成"
}

# 显示日志
show_logs() {
    log_info "显示最近的日志..."
    
    if [ -f logs/sync-refund-test.log ]; then
        echo "=== 最近 20 行日志 ==="
        tail -20 logs/sync-refund-test.log
        echo "====================="
    else
        log_warning "日志文件不存在"
    fi
}

# 主函数
main() {
    echo "开始退款同步功能测试..."
    echo
    
    # 创建日志目录
    mkdir -p logs
    
    # 设置陷阱，确保在脚本退出时清理
    trap 'stop_sync_service' EXIT
    
    # 执行测试步骤
    check_dependencies
    echo
    
    create_test_data
    echo
    
    verify_test_data
    echo
    
    start_sync_service
    echo
    
    trigger_notifications
    echo
    
    wait_for_processing
    echo
    
    verify_results
    echo
    
    show_logs
    echo
    
    log_success "测试完成！"
    echo
    
    # 询问是否清理测试数据
    read -p "是否清理测试数据？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        cleanup_test_data
    else
        log_info "保留测试数据，可手动清理"
    fi
}

# 运行主函数
main "$@"
