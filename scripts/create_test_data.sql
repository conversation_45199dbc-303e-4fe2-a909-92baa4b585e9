-- 创建测试数据脚本
-- 用于在 second_number_2 和 cohort 数据库中创建测试数据

-- 使用 second_number_2 数据库
USE second_number_2;

-- 清理现有测试数据（可选）
-- DELETE FROM all_app_orders WHERE transaction_id LIKE 'TEST_%';
-- DELETE FROM apple_server_notifications WHERE original_transaction_id LIKE 'TEST_%';
-- DELETE FROM apple_server_consumption_requests WHERE transaction_id LIKE 'TEST_%';

-- 1. 创建测试订单数据（all_app_orders表）
INSERT INTO all_app_orders (
    adam_id,
    transaction_id,
    original_transaction_id,
    bundle_id,
    product_id,
    purchase_date,
    original_purchase_date,
    quantity,
    type,
    environment,
    price,
    currency,
    country_code,
    region_id,
    sku_display_name,
    order_time,
    app_version,
    device_type,
    platform,
    storefront_id,
    storefront_country_code,
    the_day,
    created_at,
    updated_at
) VALUES 
-- 测试订单1：美国用户，$4.99应用内购买
(
    **********,  -- adam_id
    'TEST_TXN_001',  -- transaction_id
    'TEST_ORIG_TXN_001',  -- original_transaction_id
    'com.example.testapp',  -- bundle_id
    'premium_upgrade',  -- product_id
    '2024-01-15 10:30:00',  -- purchase_date
    '2024-01-15 10:30:00',  -- original_purchase_date
    1,  -- quantity
    'Non-Consumable',  -- type
    'Production',  -- environment
    499,  -- price (in cents)
    'USD',  -- currency
    'US',  -- country_code
    1,  -- region_id (美国)
    'Premium Upgrade',  -- sku_display_name
    '2024-01-15 10:30:00',  -- order_time
    '1.2.3',  -- app_version
    'iPhone',  -- device_type
    'iOS',  -- platform
    '143441',  -- storefront_id (US App Store)
    'USA',  -- storefront_country_code
    '2024-01-15',  -- the_day
    NOW(),  -- created_at
    NOW()   -- updated_at
),
-- 测试订单2：中国用户，¥30应用内购买
(
    **********,  -- adam_id
    'TEST_TXN_002',  -- transaction_id
    'TEST_ORIG_TXN_002',  -- original_transaction_id
    'com.example.testapp',  -- bundle_id
    'coins_pack_large',  -- product_id
    '2024-01-16 14:20:00',  -- purchase_date
    '2024-01-16 14:20:00',  -- original_purchase_date
    1,  -- quantity
    'Consumable',  -- type
    'Production',  -- environment
    3000,  -- price (in cents, ¥30)
    'CNY',  -- currency
    'CN',  -- country_code
    2,  -- region_id (中国)
    'Large Coins Pack',  -- sku_display_name
    '2024-01-16 14:20:00',  -- order_time
    '1.2.3',  -- app_version
    'iPhone',  -- device_type
    'iOS',  -- platform
    '143465',  -- storefront_id (China App Store)
    'CHN',  -- storefront_country_code
    '2024-01-16',  -- the_day
    NOW(),  -- created_at
    NOW()   -- updated_at
),
-- 测试订单3：日本用户，¥600订阅
(
    **********,  -- adam_id
    'TEST_TXN_003',  -- transaction_id
    'TEST_ORIG_TXN_003',  -- original_transaction_id
    'com.example.testapp',  -- bundle_id
    'monthly_subscription',  -- product_id
    '2024-01-17 09:15:00',  -- purchase_date
    '2024-01-17 09:15:00',  -- original_purchase_date
    1,  -- quantity
    'Auto-Renewable Subscription',  -- type
    'Production',  -- environment
    60000,  -- price (in cents, ¥600)
    'JPY',  -- currency
    'JP',  -- country_code
    3,  -- region_id (日本)
    'Monthly Premium Subscription',  -- sku_display_name
    '2024-01-17 09:15:00',  -- order_time
    '1.2.3',  -- app_version
    'iPad',  -- device_type
    'iOS',  -- platform
    '143462',  -- storefront_id (Japan App Store)
    'JPN',  -- storefront_country_code
    '2024-01-17',  -- the_day
    NOW(),  -- created_at
    NOW()   -- updated_at
);

-- 2. 创建测试Apple服务器通知数据（apple_server_notifications表）
INSERT INTO apple_server_notifications (
    notification_uuid,
    notification_type,
    version,
    environment,
    app_apple_id,
    bundle_id,
    receive_date,
    decoded_content,
    original_transaction_id,
    transaction_id,
    product_id,
    created_at,
    updated_at
) VALUES 
-- REFUND通知示例
(
    UUID(),  -- notification_uuid
    'REFUND',  -- notification_type
    '2.0',  -- version
    'Production',  -- environment
    **********,  -- app_apple_id
    'com.example.testapp',  -- bundle_id
    '2024-01-18 15:30:00',  -- receive_date
    '{"data":{"transaction":{"transactionId":"TEST_TXN_001","originalTransactionId":"TEST_ORIG_TXN_001","revocationDate":1705590600000,"revocationReason":1}}}',  -- decoded_content
    'TEST_ORIG_TXN_001',  -- original_transaction_id
    'TEST_TXN_001',  -- transaction_id
    'premium_upgrade',  -- product_id
    NOW(),  -- created_at
    NOW()   -- updated_at
),
-- REFUND_DECLINED通知示例
(
    UUID(),  -- notification_uuid
    'REFUND_DECLINED',  -- notification_type
    '2.0',  -- version
    'Production',  -- environment
    **********,  -- app_apple_id
    'com.example.testapp',  -- bundle_id
    '2024-01-19 10:45:00',  -- receive_date
    '{"data":{"transaction":{"transactionId":"TEST_TXN_002","originalTransactionId":"TEST_ORIG_TXN_002"}}}',  -- decoded_content
    'TEST_ORIG_TXN_002',  -- original_transaction_id
    'TEST_TXN_002',  -- transaction_id
    'coins_pack_large',  -- product_id
    NOW(),  -- created_at
    NOW()   -- updated_at
);

-- 3. 创建测试CONSUMPTION_REQUEST通知数据
INSERT INTO apple_server_notifications (
    notification_uuid,
    notification_type,
    version,
    environment,
    app_apple_id,
    bundle_id,
    receive_date,
    decoded_content,
    original_transaction_id,
    transaction_id,
    product_id,
    created_at,
    updated_at
) VALUES
-- CONSUMPTION_REQUEST通知示例1
(
    UUID(),  -- notification_uuid
    'CONSUMPTION_REQUEST',  -- notification_type
    '2.0',  -- version
    'Production',  -- environment
    **********,  -- app_apple_id
    'com.example.testapp',  -- bundle_id
    '2024-01-20 08:30:00',  -- receive_date
    '{"data":{"consumptionRequestReason":"UNINTENDED_PURCHASE","appAccountToken":"test_account_token_003","transaction":{"transactionId":"TEST_TXN_004","originalTransactionId":"TEST_ORIG_TXN_004"}}}',  -- decoded_content
    'TEST_ORIG_TXN_004',  -- original_transaction_id
    'TEST_TXN_004',  -- transaction_id
    'monthly_subscription',  -- product_id
    NOW(),  -- created_at
    NOW()   -- updated_at
),
-- CONSUMPTION_REQUEST通知示例2
(
    UUID(),  -- notification_uuid
    'CONSUMPTION_REQUEST',  -- notification_type
    '2.0',  -- version
    'Production',  -- environment
    **********,  -- app_apple_id
    'com.example.testapp',  -- bundle_id
    '2024-01-20 14:15:00',  -- receive_date
    '{"data":{"consumptionRequestReason":"DEFECTIVE","appAccountToken":"test_account_token_004","transaction":{"transactionId":"TEST_TXN_005","originalTransactionId":"TEST_ORIG_TXN_005"}}}',  -- decoded_content
    'TEST_ORIG_TXN_005',  -- original_transaction_id
    'TEST_TXN_005',  -- transaction_id
    'coins_pack_large',  -- product_id
    NOW(),  -- created_at
    NOW()   -- updated_at
);

-- 切换到 cohort 数据库，创建App配置
USE cohort;

-- 创建测试App配置（如果不存在）
INSERT IGNORE INTO app_config (
    app_name,
    developer_id,
    adam_id,
    bundle_id,
    db_connection,
    db_name,
    refund_request_supported,
    refund_request_enabled,
    apple_notifications_sync_enabled,
    refund_policy_id,
    created_at,
    updated_at
) VALUES (
    'TestApp',  -- app_name
    1,  -- developer_id
    **********,  -- adam_id
    'com.example.testapp',  -- bundle_id
    'mysql',  -- db_connection
    'second_number_2',  -- db_name
    1,  -- refund_request_supported
    1,  -- refund_request_enabled
    1,  -- apple_notifications_sync_enabled
    '9',  -- refund_policy_id
    NOW(),  -- created_at
    NOW()   -- updated_at
);

-- 显示创建的测试数据统计
SELECT 'Test data created successfully!' as status;
SELECT COUNT(*) as order_count FROM second_number_2.all_app_orders WHERE transaction_id LIKE 'TEST_%';
SELECT COUNT(*) as notification_count FROM second_number_2.apple_server_notifications WHERE original_transaction_id LIKE 'TEST_%';
SELECT COUNT(*) as consumption_request_count FROM second_number_2.apple_server_consumption_requests WHERE transaction_id LIKE 'TEST_%';
SELECT COUNT(*) as app_config_count FROM cohort.app_config WHERE adam_id = **********;
