# 旧版退款同步逻辑备忘

本文整理历史 PHP 命令 `CohortSyncAppleNotifications.php` 与 `CohortSyncRefundRequests.php` 的核心流程，方便与新的 Go 实现对照。更多关于新链路的说明见 `doc/refund-sync.md`。

## 1. 命令概览
| 文件 | 作用 |
| ---- | ---- |
| `old/CohortSyncAppleNotifications.php` | 增量同步 `apple_server_notifications`，落入统一库并更新退款状态。|
| `old/CohortSyncRefundRequests.php` | 扫描 `apple_server_consumption_requests`，创建/补齐 `refund_requests` 记录。|

两者依赖 `cohort.app_config` 的开关（`apple_notifications_sync_enabled`、`refund_request_supported`）筛选生效应用，并共享“终态保护”策略。

## 2. Apple 通知同步要点
- 使用 `apple_notifications_last_sync_id` 作为游标分批拉取通知；批量 1000 条。
- 仅处理生产环境通知（v2.0=`Production`、v1.0=`PROD`）。
- 通知类型：v2.0 支持 `REFUND` / `REFUND_DECLINED`，v1.0 仅 `REFUND`。
- Transaction ID 搜索路径：
  1. v2.0 `decoded_content.data.transaction.transactionId`
  2. v1.0 `request_content.unified_receipt.latest_receipt_info[*].transaction_id`
  3. 兜底字段 `original_transaction_id`
- 终态保护：若退款结果已是“已退款(订单轮询/收到退款通知)”则跳过。
- `REFUND` 更新 `refund_success_time`、`refund_success_hours`；`REFUND_DECLINED` 维护首次/最近拒绝时间。

这些规则在 Go 版本的 `SyncHandler` 中均已保留，并扩展了 CDC 消费能力。

## 3. 退款请求同步要点
- 针对 `refund_request_supported=1` 的应用，批量处理 `apple_server_consumption_requests` 中 `oa_sync_time IS NULL` 的行。
- 环境过滤：`environment != 'production'` 或 `adam_id` 不匹配的行直接打 `oa_sync_time` 标记跳过。
- 新建记录时：
  - `sched_process_delay_hours` 来自系统配置数组随机值。
  - `refund_policy_id` 取自 `app_config.refund_policy_id`（可多值随机），否则回退默认策略。
  - 关联 `apple_server_notifications` 补齐 `consumptionRequestReason`。
  - 关联订单表补齐 SKU、价格、地区与 `order_to_refund_request_hours`。
- 已存在记录：仅更新 `last_push_time` 与 `push_cnt`。

Go 版本通过 `sync-refund`（具体在 `SyncHandler.handleConsumptionRequestNotification` 中实现）复刻了上述行为，并在 CDC 链路中完成批量处理。

## 4. 常见注意事项
- `--limit` 调试参数若提前退出会重复处理当前批次的剩余记录，需手动确认幂等性。
- `oa_sync_time` 既表示“已同步”也用于排除非匹配数据，迁移时需保留该语义。
- 旧逻辑中有地方写错日志字段（如 `rimage.pngows to sync`），已在新实现清理。

## 5. 迁移建议
1. 优先启用 `sync-refund` CDC 流，一次替换两个 PHP 命令的功能（现已无额外 CLI 兜底），确认 CDC 消费稳定后再停用旧脚本。
2. 对比 `refund_requests` 表关键字段，确认策略 ID、终态字段与时间戳一致。
3. 若需要扩展 CDC 行为，可在 `doc/refund-sync.md` 的“已知限制”章节找到当前 Go 实现尚未覆盖的 TODO 并补齐。
