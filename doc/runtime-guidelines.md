# 运行时指南：Kafka 配置与链路追踪

本文整合 Kafka 客户端可调参数与链路追踪 Header 规范，便于在不同环境中调优与排障。

## 1. Kafka 客户端参数
`internal/client/kafkax.go` 基于 franz-go 封装，所有参数通过 `KafkaOptions` 控制。默认值面向 CDC 场景，以下列出常用项：

| 分类 | 字段 | 默认值 | 说明 |
| ---- | ---- | ------ | ---- |
| 基本 | `Brokers` | 继承配置 | Kafka broker 地址列表，若为空则使用 `config.Kafka.Brokers`。|
| 生产者 | `ProducerBatchSize` | 100 | 每批消息的最大数量（内部下限 1MB）。|
| 生产者 | `ProducerBatchTimeout` | 10ms | 批次发送超时。|
| 生产者 | `ProducerRequiredAcks` | 1 | 等待 leader 确认，平衡可靠性与性能。|
| 生产者 | `ProducerRetryMax` | 3 | 发送失败重试次数。|
| 消费者 | `ConsumerStartOffset` | -2 | 对应 `FirstOffset`，保证从历史开始消费。|
| 消费者 | `MaxConcurrency` | 1 | 单消息顺序处理；可按业务需求提升。|
| 消费者 | `MessageTimeout` | 30s | 单条消息处理超时。|
| 错误处理 | `EnableDeadLetterQueue` | false | 是否启用死信队列。|
| 错误处理 | `DeadLetterTopic` | 空 | DLQ 主题名称。|
| 错误处理 | `MaxRetries` | 3 | 业务回调失败时的重试次数。|

### 1.1 自定义示例
```go
opts := client.DefaultKafkaOptions()
opts.MaxConcurrency = 4
opts.EnableDeadLetterQueue = true
opts.DeadLetterTopic = "midas.refund.dlq"

k := client.NewKafka(config.Get(), opts)
```

在开启 DLQ 后，未处理成功的消息会携带原始 Headers 写入指定主题，可配合 Kafka-UI 或额外消费者诊断问题。

## 2. 链路追踪 Header 规范
`internal/pkg/trace` 定义了两类 Header：

### 2.1 透传类（`X-Passthrough-*`）
| Header | 含义 |
| ------ | ---- |
| `X-Passthrough-Trace-Id` | 全链路唯一 ID，16 位十六进制，末两位代表来源（00=App、03=Service、04=MQ 等）。|
| `X-Passthrough-Run-Mode` | 运行模式：`prod` / `test` / `replay`。|
| `X-Passthrough-Source-Ip` | 发起方 IP。|
| `X-Passthrough-Source-Name` | 发起服务名称。|
| `X-Passthrough-Source-Time` | 发起时间戳（RFC3339）。|
| `X-Passthrough-Retry-Count` | 重试次数。|

### 2.2 计算类（`X-Compute-*`）
| Header | 含义 |
| ------ | ---- |
| `X-Compute-Timestamp` | 当前处理时间。|
| `X-Compute-Span-Id` | 当前跨度 ID。|
| `X-Compute-Parent-Span-Id` | 上游跨度 ID。|
| `X-Compute-Caller` / `X-Compute-Caller-Func` | 调用方名称与入口方法。|
| `X-Compute-Callee` / `X-Compute-Callee-Func` | 被调用方名称与方法。|

消费 Kafka 消息时，`Kafka.Consume` 会将 Headers 恢复到上下文；生产消息时自动注入新的 Trace Context。

### 2.3 使用片段
```go
headers := map[string]string{
    trace.HeaderTraceID: trace.GenerateTraceID(trace.SourceService),
    trace.HeaderRunMode: string(trace.ModeTest),
}

if err := client.GetKafka().Produce(ctx, topic, nil, payload, headers); err != nil {
    logx.WithContext(ctx).WithError(err).Error("produce failed")
}
```

在 HTTP / gRPC 场景中复用同一套 Header，可简化链路追踪日志的串联。

## 3. 调试建议
- `LOG_LEVEL=debug` 可打印 Kafka 消息的 `topic/partition/offset` 与 Trace ID。
- 若消费出现超时，可增大 `MessageTimeout` 或提高 `MaxConcurrency`，同时关注数据库锁等待。
- 使用 `docker/README.md` 中的 Kafka-UI，通过过滤 `apps.` 前缀主题排查 CDC 事件结构。
