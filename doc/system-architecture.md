# 系统架构与代码结构

本项目采用 Go Module + 内部包的方式组织离线 CLI。以下内容聚焦于核心目录、关键组件以及数据流走向，帮助快速熟悉代码。

## 1. 目录总览
```
midas/
├── cmd/cli/                 # 可执行入口（Cobra）
├── internal/
│   ├── cli/                # 具体指令：refund、kafka 工具等
│   ├── client/             # MySQL / Redis / HTTP / Kafka 封装
│   ├── config/             # TOML 配置加载与校验
│   ├── models/             # GORM 模型与领域逻辑
│   └── pkg/
│       ├── binlog/        # Debezium 消息解析
│       ├── ctxx/          # run_id / trace_id 上下文
│       ├── logx/          # 结构化日志初始化
│       ├── refund/        # Refund CDC 同步处理器
│       └── trace/         # 链路追踪工具
├── configs/                # 默认配置
├── docker/                 # 本地 Kafka & Debezium 环境
├── doc/                    # 文档集合
└── scripts/                # （迁移后仅保留辅助脚本）
```

## 2. 核心模块说明

### 2.1 CLI 框架（`cmd/cli` + `internal/cli`）
- 使用 Cobra 管理子命令；`root.go` 实现统一的配置加载、日志初始化与客户端初始化。
- `process_refund_requests.go`、`sync_refund.go` 等文件分别对应一条命令；通过 `ctxx.WithBothIDs` 保证日志中包含 `run_id`、`trace_id`。
- Kafka 工具命令（`kafka_produce`、`kafka_consume`、`kafka_multi_consume`）依赖统一的 Kafka 客户端，便于手动调试。

### 2.2 客户端抽象（`internal/client`）
- `InitClients()` 在 Root 命令阶段执行，只初始化配置中出现的组件，避免不必要的依赖。
- MySQL：采用 GORM，`GetDB()` 返回统一库连接，`GetDBForDatabase()` 基于主 DSN 拆库名并缓存各业务库连接。
- Kafka：默认使用 franz-go，`KafkaOptions` 提供批次大小、重试、DLQ 等参数（详见 `doc/runtime-guidelines.md`）。
- HTTP 与 Redis：按需初始化，留作 ASC 客户端与缓存使用。

### 2.3 Refund 同步处理（`internal/pkg/refund`）
- `SyncHandler` 将 Debezium 事件转换为业务操作，处理 Transaction ID 提取、终态判定、订单关联等细节。
- 通过 `models` 层访问数据库字段，保持业务逻辑与数据访问的一致性。

### 2.4 GORM 模型（`internal/models`）
- 覆盖 `app_config`、`refund_requests`、`apple_server_notifications` 等表。
- 提供少量业务方法（如 `GetOrderByTransactionID`、`ComputeRefundInfo`）以封装字段含义。

## 3. 运行时数据流

1. **配置加载**：
   - `config.Load` 从 TOML 解析出结构体并校验（包括时间格式、Kafka 配置完整性）。
   - 全局配置通过 `config.Get()` 访问。

2. **命令执行**：
   - 每条命令在 `RunE` 中获取上下文日志器，记录批次/耗时等信息。
   - 数据库操作统一使用 UTC 时间，避免时区混乱。

3. **CDC 消费**：
   - Kafka 客户端在消费回调中为每条消息生成独立 Trace 上下文，Headers 中透传 `X-Passthrough-*` 信息。
   - 同步处理器将 Debezium 消息解析为 `ChangeEvent`，根据表/操作路由。

4. **日志与追踪**：
   - `logx` 负责初始化输出位置（按小时切分），并与 `trace` 包联动输出 `trace_id`。

## 4. 扩展切入点
- **策略调整**：在 `models.ComputeRefundInfo` 及 `internal/pkg/refund` 中添加新字段计算或新增策略类型。
- **ASC API 集成**：替换 `callAppleASCStub`，利用 `client.GetHTTP()` 与 `config.Apple` 完成真实回传。
- **更多 CDC 表**：在 `sync_refund.go` 中扩展 `filterRefundSubscriptions`、`handleRefundMessage` 即可接入其他主题。
- **监控指标**：`KafkaOptions.EnableMetrics` 与 `trace` 包可用于接入 Prometheus、OpenTelemetry 等采集。

## 5. 依赖与版本
- Go 1.22+
- GORM (`gorm.io/gorm`) + MySQL Driver (`gorm.io/driver/mysql`)
- franz-go (`github.com/twmb/franz-go`) 作为 Kafka 客户端
- Cobra (`github.com/spf13/cobra`)、Viper (`github.com/spf13/viper`)

了解细节后，可搭配 `doc/refund-sync.md` 深入具体流程，或参考 `docker/README.md` 构建本地 CDC 测试环境。
