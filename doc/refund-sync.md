# 退款同步与处理指南

本文档聚焦当前仍在维护的两条指令：`sync-refund`（CDC 主流程）与 `process-refund-requests`（消费回传），说明它们在苹果退款场景中的职责与配置要点。

## 1. 设计目标
- **统一链路**：整合旧有 `CohortSyncRefundRequests.php` 与 `CohortSyncAppleNotifications.php` 的逻辑，交由单一 CDC 消费链路完成。
- **实时同步**：借助 Debezium + Kafka 的 CDC 事件，在秒级别感知 `apple_server_notifications` 表的变更。
- **终态保护**：避免“已退款”状态被重复写入覆盖，保留拒绝时间等关键字段。
- **策略化处理**：在补齐退款请求时预留策略延迟、订单补全等能力，为后续回传提供上下文。

## 2. 数据流概览
```
MySQL(app) ── Debezium ──▶ Kafka ──▶ midas sync-refund
      ▲                                    │
      │                                    ├─ CONSUMPTION_REQUEST → refund_requests 新增/更新
      │                                    ├─ REFUND             → 终态 & refund_success_time
      │                                    └─ REFUND_DECLINED    → first/last_decline_time
```

- CDC 消费依赖 `configs/app.toml` 中的 `[[kafka.subscriptions]]`，必须启用 `apple_notifications_cdc` 并配置正确的主题与消费组。
- `sync-refund` 在消费过程中同时承担“补齐消费请求”“同步通知终态”“关联订单信息”等职责。

## 3. 指令一览

### 3.1 sync-refund（CDC 主流程）
```bash
./bin/midas sync-refund --config configs/app.toml
```

- 读取配置中启用的订阅，仅消费 `apple_notifications_cdc`。
- 使用 franz-go 消费 Debezium JSON，将消息解析为 `ChangeEvent` 并交由 `internal/pkg/refund.SyncHandler` 处理。
- **CONSUMPTION_REQUEST**：若 `refund_requests` 中不存在对应 `transaction_id`，创建一条记录，回填通知字段、策略信息、`oa_sync_time`，并尝试关联订单数据。
- 已存在记录时，仅更新 `push_cnt` 与 `last_push_time`，保证幂等。
- **REFUND**：在非终态记录上写入 `refund_result = "已退款(收到退款通知)"`、`refund_success_time` 及 `refund_success_hours`（首推送到成功的小时差）。
- **REFUND_DECLINED**：维护 `first_refund_decline_time`、`last_refund_decline_time`。
- 终态判定：当退款结果为 `已退款(订单轮询)` 或 `已退款(收到退款通知)` 时跳过更新。
- 调试模式：`midas sync-refund debug [--pretty=false]` 验证数据库连接、Kafka 连接和配置是否正确。

### 3.2 process-refund-requests
```bash
./bin/midas process-refund-requests
```

- 选取配置中启用退款处理的 `adam_id`，挑出 `sched_process_time` 已到、尚未处理的记录。
- 依据策略配置（`refund_policy_id`）调用 `models.ComputeRefundInfo` 生成消费信息，并写回多个统计字段。
- 当前实现以 `callAppleASCStub` 占位，默认返回 200；若实际接入 ASC API，需要替换为真实客户端并处理失败重试。
- 超过 `refund.max_processing_days` 的请求打上 `process_result="超时跳过"` 并写入说明。

## 4. 配置要点

| 模块 | 关键字段 | 说明 |
| ---- | -------- | ---- |
| MySQL | `mysql.dsn` | 指向统一库 `cohort`，程序会基于该 DSN 拼接 `app_config.db_name`。 |
| Refund | `sched_process_delay_hours`、`default_policy_id` | 为历史批处理兼容保留，CDC 链路将逐步接入该配置以决定调度延迟与策略 ID（当前仍按 TODO 实现）。|
| Kafka | `brokers`, `topic`, `group_id` | `Load` 会在 broker 配置存在时强制要求 legacy `topic` 字段，确保兼容老命令。|
| Kafka 订阅 | `apple_notifications_cdc` | 必须启用；主题应指向 Debezium 产出的 `apps.<db>.apple_server_notifications`。|

## 5. 数据库期望
- 统一库：`cohort.app_config` 提供 `db_name`、`adam_id`、`refund_request_supported`、`refund_request_enabled`、`apple_notifications_sync_enabled` 等开关。
- 业务库：需包含 `apple_server_consumption_requests`、`apple_server_notifications` 表；CDC 流也来自后者。
- 订单数据：`models.GetOrderByTransactionID` 会根据 `adam_id + transaction_id` 匹配，并补齐 SKU / 价格 / 下单时间。

## 6. 已知限制与 TODO
- `sync-refund` 中 `sched_process_delay_hours` 仍写死为 0，需要结合配置补齐。
- `getRefundPolicyID` 默认返回 9，当应用或系统配置缺失时需要补全策略来源。
- Apple Store Connect 客户端目前为占位实现；上线前需替换并补齐重试、错误处理。

## 7. 调试与排障建议
- 使用 `sync-refund debug --message/--file` 检查单条 Debezium payload 的解析结果。
- 配置 `LOG_LEVEL=debug`（通过 logx）后，可观察批次进度与关键字段。
- 结合 `docker/README.md` 提供的本地 Kafka-UI 与 Connect 界面验证 CDC 事件是否齐全。

更多与旧版 PHP 逻辑的字段映射可参考 `doc/legacy-refund-sync.md`。
