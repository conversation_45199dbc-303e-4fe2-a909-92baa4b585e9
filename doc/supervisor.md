# Supervisor 部署参考

本指南提供在生产环境中使用 Supervisor 管理 Midas CLI 的示例。请根据自身目录结构与用户权限进行调整。

## 1. 目标进程
| 程序 | 场景 | 建议策略 |
| ---- | ---- | -------- |
| `midas process-refund-requests` | 常驻处理队列，周期性查询数据库 | 持续运行，失败自动重启 |
| `midas sync-refund` | 消费 Kafka CDC，需 7x24 保持在线 | 常驻运行，注意健康检查与日志轮转 |

## 2. 示例配置
假设二进制位于 `/opt/midas/bin/midas`，配置文件位于 `/etc/midas/app.toml`。

```ini
[program:midas-process-refund]
command=/opt/midas/bin/midas process-refund-requests --config /etc/midas/app.toml
directory=/opt/midas
user=midas
autostart=true
autorestart=true
startsecs=5
stopsignal=TERM
stdout_logfile=/var/log/midas/process.log
stderr_logfile=/var/log/midas/process.wf.log
stdout_logfile_maxbytes=100MB
stdout_logfile_backups=20

[program:midas-sync-refund]
command=/opt/midas/bin/midas sync-refund --config /etc/midas/app.toml
directory=/opt/midas
user=midas
autostart=true
autorestart=true
startsecs=5
stopsignal=TERM
stdout_logfile=/var/log/midas/refund.log
stderr_logfile=/var/log/midas/refund.wf.log
stdout_logfile_maxbytes=200MB
stdout_logfile_backups=20
```

## 3. 部署步骤
1. **复制配置**：
   ```bash
   sudo cp scripts/supervisor/*.conf /etc/supervisor/conf.d/  # 若使用示例文件
   ```
2. **创建运行用户与目录**：
   ```bash
   sudo useradd -r -s /bin/false midas
   sudo mkdir -p /opt/midas /etc/midas /var/log/midas
   sudo chown -R midas:midas /opt/midas /var/log/midas
   ```
3. **放置二进制与配置**：
   ```bash
   sudo cp bin/midas /opt/midas/bin/
   sudo cp configs/app.toml /etc/midas/app.toml
   sudo chmod +x /opt/midas/bin/midas
   ```
4. **加载 Supervisor**：
   ```bash
   sudo supervisorctl reread
   sudo supervisorctl update
   ```

## 4. 运维要点
- **日志监控**：
  ```bash
  sudo supervisorctl tail -f midas-process-refund stdout
  sudo supervisorctl tail -f midas-sync-refund stderr
  ```
- **健康检查**：关注 `process_result="回传失败"` 或 Kafka 消费延迟，必要时结合 Prometheus / 日志告警。
- **资源限制**：可通过 `/etc/security/limits.conf` 或 systemd cgroup 设置内存、CPU 限制，防止阻塞其他服务。

## 5. 故障排查
| 症状 | 排查方向 |
| ---- | -------- |
| 进程频繁重启 | 检查配置文件路径、数据库/Kafka 连通性、日志目录写权限。|
| `sync-refund` 停滞 | 使用 `docker/README.md` 中的 Kafka-UI 查看主题消息堆积，或启用 debug 日志观察 offset。|
| 数据未写入 | 确认 `cohort.app_config` 中对应开关（`refund_request_enabled`、`apple_notifications_sync_enabled`）是否开启。|

根据业务要求调整日志保留周期、重试策略等参数，确保与 `doc/runtime-guidelines.md` 的调优建议保持一致。
