# 项目规则（Cursor Rules, CLI 版｜通用）

> 面向一次性命令行任务（非守护进程、无端口），仅包含通用限制与最小必需约束；业务细节与流程在独立 Prompt 中定义。

---

## 运行形态与调度

* 形态：一次性 CLI 任务，进程内完成后正常退出。
* 外部调度：统一由 **supervisor** 触发与守护（拉起、重启、退出处理由 supervisor 负责）。
* 不监听端口，不作为常驻服务运行。

---

## 日志规范

* 路径：`./logs/app.log` 与 `./logs/app.log.wf`（`wf` 记录 `warn+`）。
* 滚动：按小时滚动；文件名后缀为 `YYYYMMDDHH`，例如 `./logs/app.log.2025091514`、`./logs/app.log.wf.2025091514`。
* 保留：7 天（超期自动清理）。
* 时间：日志必须包含时间信息（建议 RFC3339，时区全局一致）。
* 基础字段：默认包含 `run_id`、`trace_id` 等上下文字段，便于排障与串联。

---

## Go 与依赖边界

* 语言版本：使用最新稳定版 Go（以仓库 `go.mod` 为准）。
* HTTP 客户端：**Resty**（`github.com/go-resty/resty/v2`）。
* 数据库 ORM：**GORM**（`gorm.io/gorm`，驱动 `gorm.io/driver/mysql`）。
* 缓存：**go-redis v9**（`github.com/redis/go-redis/v9`）。
* 配置：**TOML** 文件（位于 `configs/` 目录），必要项启动时校验。
* 迁移：暂不考虑（本规则不涉及迁移脚本与约定）。
* 校验/辅助测试：本规则不涉及。
* 并发控制：本规则不涉及。

---

## 目录结构（建议最小骨架）

* `cmd/cli/`：命令入口。
* `internal/cli/`：根命令与子命令的组织（仅通用部分，不耦合业务）。
* `internal/config/`：TOML 配置读取、默认值、环境覆盖、校验。
* `internal/client/`：外部依赖初始化（HTTP/GORM/Redis）。
* `internal/pkg/logx/`：日志（按小时滚动、7 天保留）。
* `internal/pkg/ctxx/`：上下文工具（`run_id`、`trace_id`）。
* `configs/`：配置文件目录（如 `app.toml`）。
* `logs/`：日志目录（由程序或部署脚本创建）。

> 以上为分层建议，具体实现细节与命名可按仓库实际落地调整。

---

## 配置（TOML）要求

* 仅使用 **TOML**；缺失或不合法的关键配置在启动阶段立即失败并退出。
* 字段分组建议（字段命名可根据实际落地）：

  * `app`：`name`、`env`、`log_dir` 等通用信息。
  * `http`：`timeout`（默认 10s）、`retry_count`、`retry_wait`（默认 1s）。

    * 代理不在配置中出现；如需代理，在生成 HTTP 客户端时以参数方式注入。
  * `mysql`：`dsn`、`max_idle`、`max_open`、`conn_max_lifetime`。
  * `redis`：`addr`、`db`、`username`、`password`、`pool_size`。

---

## 上下文与链路信息

* 所有对外（跨层）方法的首参为 `context.Context`。
* 进入命令后即创建或接入 `run_id` 与 `trace_id`，并在日志中透传。
* 进程退出前，确保关键日志（含 `run_id`、`trace_id`、耗时、统计等）落盘。

---

## HTTP（Resty）统一约束

* 默认超时 10 秒，可由配置覆盖。
* 重试由配置控制开关与次数；`retry_wait` 固定默认 1 秒，也可在生成 HTTP 客户端时作为参数注入。
* 若在生成 HTTP 客户端时注入了代理，启动阶段必须验证代理可达；不可达时直接报错退出，不允许降级直连。
* 可使用 Resty 的响应钩子进行统一错误记录与通用指标上报（不承载业务判断）。

---

## MySQL（GORM）约束

* 单例：整个进程生命周期内仅创建一个 MySQL 客户端实例，需保证进程安全。
* 连接池：根据配置设置 `max_idle`、`max_open`、`conn_max_lifetime`。
* 健康校验：启动阶段进行连通性校验，失败立即退出。

---

## Redis 约束

* 单例：整个进程生命周期内仅创建一个 Redis 客户端实例，需保证进程安全。
* 连接池：根据配置设置 `pool_size` 与超时。
* 健康校验：启动阶段进行 PING 校验，失败立即退出。

---

## 仓库与清洁度

* 正确维护 `.gitignore`，确保以下类别文件不被提交：日志目录（如 `logs/`）、构建产物（如 `bin/`）、IDE 配置（如 `.idea/`、`.vscode/`）、系统临时文件等。
* 不需要 `.dockerignore`、`.editorconfig`。
