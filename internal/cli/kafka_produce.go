package cli

import (
	"fmt"
	"midas/internal/client"
	"midas/internal/config"
	"midas/internal/pkg/ctxx"
	"midas/internal/pkg/logx"
	"strings"
	"time"

	"github.com/spf13/cobra"
)

var (
	produceTopic    string
	produceBrokers  string
	produceKey      string
	produceValue    string
	produceCount    int
	produceInterval time.Duration
)

var kafkaProduceCmd = &cobra.Command{
	Use:   "kafka-produce",
	Short: "向 Kafka 发送消息（demo）",
	RunE: func(cmd *cobra.Command, args []string) error {
		ctx := ctxx.WithBothIDs(cmd.Context())
		logger := logx.WithContext(ctx).WithField("command", "kafka-produce")

		cfg := config.Get()
		if cfg == nil {
			return fmt.Errorf("config not loaded")
		}

		brokers := make([]string, 0)
		if strings.TrimSpace(produceBrokers) != "" {
			for _, b := range strings.Split(produceBrokers, ",") {
				b = strings.TrimSpace(b)
				if b != "" {
					brokers = append(brokers, b)
				}
			}
		}
		if len(brokers) == 0 {
			brokers = cfg.Kafka.Brokers
		}
		if len(brokers) == 0 {
			return fmt.Errorf("no kafka brokers configured; set [kafka].brokers or --brokers")
		}

		topic := produceTopic
		if topic == "" {
			return fmt.Errorf("no kafka topic provided; use --topic")
		}

		if produceCount <= 0 {
			produceCount = 1
		}

		logger.WithFields(map[string]interface{}{
			"topic":   topic,
			"brokers": strings.Join(brokers, ","),
			"count":   produceCount,
		}).Info("producing messages")

		k := client.GetKafka()
		if k == nil {
			return fmt.Errorf("kafka client is not initialized")
		}

		// 使用新的简化 API
		for i := 0; i < produceCount; i++ {
			// 添加消息序号到 headers
			headers := map[string]string{
				"message_index": fmt.Sprintf("%d", i),
				"total_count":   fmt.Sprintf("%d", produceCount),
			}

			if err := k.Produce(ctx, topic, []byte(produceKey), []byte(produceValue), headers); err != nil {
				return fmt.Errorf("failed to produce message %d: %w", i+1, err)
			}

			logger.WithFields(map[string]interface{}{
				"message_index": i + 1,
				"total_count":   produceCount,
				"key":           produceKey,
			}).Debug("message produced")

			if produceInterval > 0 && i < produceCount-1 {
				time.Sleep(produceInterval)
			}
		}

		logger.Info("produce finished")
		return nil
	},
}

func init() {
	kafkaProduceCmd.Flags().StringVar(&produceTopic, "topic", "", "Kafka 主题（默认读取配置）")
	kafkaProduceCmd.Flags().StringVar(&produceBrokers, "brokers", "", "Kafka broker 列表，逗号分隔（默认读取配置）")
	kafkaProduceCmd.Flags().StringVar(&produceKey, "key", "", "消息 Key")
	kafkaProduceCmd.Flags().StringVar(&produceValue, "message", "hello, midas", "消息内容")
	kafkaProduceCmd.Flags().IntVar(&produceCount, "count", 1, "发送条数")
	kafkaProduceCmd.Flags().DurationVar(&produceInterval, "interval", 0, "每条消息之间的间隔")
}
