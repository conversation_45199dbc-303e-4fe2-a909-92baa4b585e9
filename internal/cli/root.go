package cli

import (
	"context"
	"fmt"
	"midas/internal/client"
	"midas/internal/config"
	"midas/internal/pkg/logx"

	"github.com/spf13/cobra"
)

var (
	configFile string
	verbose    bool
)

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "midas",
	Short: "Midas - 支付离线功能工具",
	Long: `Midas 是一个支付离线功能工具，主要用于：
- 苹果退款回传处理
- 退款数据整理和验证
- 支付状态跟踪
- 多平台支付处理能力`,
	PersistentPreRunE: func(cmd *cobra.Command, args []string) error {
		// 加载配置
		if err := config.Load(configFile); err != nil {
			return fmt.Errorf("failed to load config: %w", err)
		}

		// 初始化日志
		if err := logx.Init(); err != nil {
			return fmt.Errorf("failed to init logger: %w", err)
		}

		// 初始化外部客户端
		if err := client.InitClients(); err != nil {
			return fmt.Errorf("failed to init clients: %w", err)
		}

		return nil
	},
}

// Execute adds all child commands to the root command and sets flags appropriately.
func Execute(ctx context.Context) error {
	return rootCmd.ExecuteContext(ctx)
}

func init() {
	// 全局标志
	rootCmd.PersistentFlags().StringVar(&configFile, "config", "configs/app.toml", "配置文件路径")
	rootCmd.PersistentFlags().BoolVarP(&verbose, "verbose", "v", false, "详细输出")

	// 添加子命令
	rootCmd.AddCommand(processRefundRequestsCmd)
	rootCmd.AddCommand(kafkaProduceCmd)
	rootCmd.AddCommand(kafkaConsumeCmd)
	rootCmd.AddCommand(kafkaMultiConsumeCmd)
	rootCmd.AddCommand(syncRefundCmd)
}
