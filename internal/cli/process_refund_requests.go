package cli

import (
	"context"
	"fmt"
	"midas/internal/client"
	"midas/internal/config"
	"midas/internal/models"
	"midas/internal/pkg/ctxx"
	"midas/internal/pkg/logx"
	"time"

	"github.com/spf13/cobra"
)

var processRefundRequestsCmd = &cobra.Command{
	Use:   "process-refund-requests",
	Short: "处理苹果退款请求并回传消费信息",
	Long: `处理待处理的退款请求，向苹果回传用户消费信息以影响退款决策。

功能说明：
- 根据退款策略计算消费信息
- 调用Apple Store Connect API回传数据
- 记录处理结果和时间统计
- 跳过超过时间限制的请求`,
	RunE: func(cmd *cobra.Command, args []string) error {
		ctx := cmd.Context()
		traceID := ctxx.GetTraceID(ctx)
		runID := ctxx.GetRunID(ctx)

		logger := logx.WithContext(ctx).WithFields(map[string]interface{}{
			"command":  "process-refund-requests",
			"run_id":   runID,
			"trace_id": traceID,
		})

		startTime := time.Now()
		logger.Info("开始处理退款请求")

		// TODO: 实现处理逻辑
		err := processRefundRequestsHandler(ctx)

		duration := time.Since(startTime)
		if err != nil {
			logger.WithError(err).WithField("duration", duration).Error("处理退款请求失败")
			return err
		}

		logger.WithField("duration", duration).Info("处理退款请求完成")
		return nil
	},
}

func processRefundRequestsHandler(ctx context.Context) error {
	logger := logx.WithContext(ctx)

	db := client.GetDB()
	if db == nil {
		return fmt.Errorf("unified DB client not initialized")
	}

	// find enabled adam ids
	var enabledAdamIDs []uint
	if err := db.Model(&models.AppConfig{}).
		Where("refund_request_enabled = ?", true).
		Pluck("adam_id", &enabledAdamIDs).Error; err != nil {
		return err
	}
	if len(enabledAdamIDs) == 0 {
		logger.Info("No apps enabled for refund requests")
		return nil
	}

	// find pending refund requests
	var requests []models.RefundRequest
	if err := db.Where("adam_id IN ? AND process_time IS NULL AND sched_process_time < ?", enabledAdamIDs, time.Now().UTC()).
		Order("sched_process_time ASC").
		Find(&requests).Error; err != nil {
		return err
	}
	if len(requests) == 0 {
		logger.Info("No refund request to process")
		return nil
	}

	cfg := config.Get()
	maxDays := cfg.Refund.MaxProcessingDays
	for i, rr := range requests {
		idx := i + 1
		itemLogger := logger.WithFields(map[string]interface{}{
			"i": idx, "n": len(requests), "id": rr.ID, "app_name": safeStr(rr.AppName), "transaction_id": safeStr(rr.TransactionID),
		})
		nowUTC := time.Now().UTC()
		// set process_time and delay hours
		var delayHours float64
		if rr.FirstPushTime != nil {
			delayHours = float64(nowUTC.Sub(*rr.FirstPushTime).Minutes()) / 60.0
			delayHours = float64(int(delayHours*10+0.5)) / 10.0
		}
		_ = db.Model(&rr).Updates(map[string]interface{}{
			"process_time":        nowUTC,
			"process_delay_hours": delayHours,
		}).Error

		// skip if older than max days
		if rr.FirstPushTime != nil {
			if nowUTC.Sub(*rr.FirstPushTime) > (time.Duration(maxDays) * 24 * time.Hour) {
				itemLogger.Info("Skip: refund request older than max days")
				_ = db.Model(&rr).Updates(map[string]interface{}{
					"process_result":        "超时跳过",
					"process_response_code": 501,
					"process_response_body": fmt.Sprintf("refund request older than %d days", maxDays),
				}).Error
				continue
			}
		}

		// compute refund info
		var policy *models.RefundPolicy
		if rr.RefundPolicyID != nil {
			var p models.RefundPolicy
			if err := db.First(&p, *rr.RefundPolicyID).Error; err == nil {
				policy = &p
			}
		}
		info := models.ComputeRefundInfo(policy, derefInt64(rr.AdamID), safeStr(rr.TransactionID), safeStr(rr.AppAccountToken))

		// update fields from computed info
		var dl2ReqHours *float64
		if info.ConsumptionInfo.DownloadTime != nil && rr.FirstPushTime != nil {
			d := info.ConsumptionInfo.DownloadTime
			// d is *string or *time? In our struct it's *string. Attempt to parse RFC3339 if provided
			if d != nil && *d != "" {
				if tm, err := time.Parse(time.RFC3339, *d); err == nil {
					h := float64(int((tm.Sub(*rr.FirstPushTime).Minutes()/60.0)*10+0.5)) / 10.0
					dl2ReqHours = &h
				}
			}
		}
		_ = db.Model(&rr).Updates(map[string]interface{}{
			"region_id":                        info.OrderInfo.RegionID,
			"sku_display_name":                 info.OrderInfo.SkuDisplayName,
			"sku_price":                        info.OrderInfo.SkuPrice,
			"refund_success_time":              info.OrderInfo.RefundSuccessTime,
			"download_time":                    info.ConsumptionInfo.DownloadTime,
			"download_to_refund_request_hours": dl2ReqHours,
			"download_to_process_days":         info.ConsumptionInfo.DiffInDays,
			"download_to_process_hours":        info.ConsumptionInfo.DiffInHours,
			"download_to_process_minutes":      info.ConsumptionInfo.DiffInMinutes,
		}).Error

		if info.OrderInfo.RefundSuccessTime != nil && rr.FirstPushTime != nil {
			now := nowUTC
			rrHours := float64(int((now.Sub(*rr.FirstPushTime).Minutes()/60.0)*10+0.5)) / 10.0
			_ = db.Model(&rr).Updates(map[string]interface{}{
				"refund_result":             "已退款",
				"refund_result_track_time":  now,
				"refund_result_track_hours": rrHours,
				"refund_success_hours":      float64(now.Sub(*rr.FirstPushTime).Hours()),
			}).Error
		}

		_ = db.Model(&rr).Updates(map[string]interface{}{
			"account_tenure":             info.ConsumptionInfo.AccountTenure,
			"consumption_status":         info.ConsumptionInfo.ConsumptionStatus,
			"customer_consented":         boolToYN(info.ConsumptionInfo.CustomerConsented),
			"delivery_status":            info.ConsumptionInfo.DeliveryStatus,
			"lifetime_dollars_purchased": info.ConsumptionInfo.LifetimeDollarsPurchased,
			"lifetime_dollars_refunded":  info.ConsumptionInfo.LifetimeDollarsRefunded,
			"platform":                   info.ConsumptionInfo.Platform,
			"play_time":                  info.ConsumptionInfo.PlayTime,
			"refund_preference":          info.ConsumptionInfo.RefundPreference,
			"sample_content_provided":    boolToYN(info.ConsumptionInfo.SampleContentProvided),
			"user_status":                info.ConsumptionInfo.UserStatus,
		}).Error

		// Call Apple Store Connect API - stub using HTTP client; status 200 for now
		statusCode, resBody := callAppleASCStub(ctx, rr)
		if statusCode >= 200 && statusCode < 300 {
			_ = db.Model(&rr).Updates(map[string]interface{}{
				"process_result":        "回传成功",
				"process_response_code": statusCode,
				"process_response_body": resBody,
			}).Error
		} else {
			_ = db.Model(&rr).Updates(map[string]interface{}{
				"process_result":        "回传失败",
				"process_response_code": statusCode,
				"process_response_body": resBody,
			}).Error
		}
	}

	return nil
}

func boolToYN(b bool) string {
	if b {
		return "1"
	}
	return "0"
}

func callAppleASCStub(ctx context.Context, rr models.RefundRequest) (int, string) {
	// Placeholder for real ASC client; ensure Resty timeout is respected
	_ = client.GetHTTP() // not used yet
	return 200, "OK"
}
