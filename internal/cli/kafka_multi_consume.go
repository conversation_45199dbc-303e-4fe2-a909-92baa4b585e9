package cli

import (
	"context"
	"fmt"
	"midas/internal/client"
	"midas/internal/config"
	"midas/internal/pkg/logx"
	"os"
	"os/signal"
	"syscall"

	"github.com/spf13/cobra"
)

// kafkaMultiConsumeCmd 多订阅消费命令
var kafkaMultiConsumeCmd = &cobra.Command{
	Use:   "kafka-multi-consume",
	Short: "Start multiple Kafka subscriptions",
	Long:  `Start multiple Kafka subscriptions based on configuration`,
	RunE:  runKafkaMultiConsume,
}

func init() {
	kafkaMultiConsumeCmd.Flags().BoolP("verbose", "v", false, "Enable verbose logging")
}

func runKafkaMultiConsume(cmd *cobra.Command, args []string) error {
	verbose, _ := cmd.Flags().GetBool("verbose")

	// 设置日志级别
	if verbose {
		// logx 不支持动态设置级别，这里只是标记
	}

	// 加载配置
	cfg := config.Get()
	if cfg == nil {
		return fmt.Errorf("config not loaded")
	}

	// 检查是否有订阅配置
	if len(cfg.Kafka.Subscriptions) == 0 {
		return fmt.Errorf("no kafka subscriptions configured")
	}

	// 创建 Kafka 客户端（使用默认的 franz-go）
	kafka := client.GetKafka()
	if kafka == nil {
		return fmt.Errorf("kafka client is not initialized")
	}

	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		<-sigChan
		fmt.Println("\nReceived interrupt signal, shutting down...")
		cancel()
	}()

	logger := logx.WithContext(ctx)
	logger.WithFields(map[string]interface{}{
		"provider":      "franz-go",
		"subscriptions": len(cfg.Kafka.Subscriptions),
	}).Info("starting multi-subscription kafka consumer")

	// 启动多订阅消费
	err := kafka.ConsumeSubscriptions(ctx, cfg.Kafka.Subscriptions, func(ctx context.Context, subscription config.KafkaSubscription, message client.Message) error {
		// 处理消息
		logger := logx.WithContext(ctx).WithFields(map[string]interface{}{
			"subscription": subscription.Name,
			"group_id":     subscription.GroupID,
			"topic":        message.Topic,
			"partition":    message.Partition,
			"offset":       message.Offset,
		})

		logger.WithFields(map[string]interface{}{
			"key":   string(message.Key),
			"value": string(message.Value),
		}).Info("received message")

		// 打印到控制台
		fmt.Printf("[%s] Topic: %s, Partition: %d, Offset: %d, Key: %s, Value: %s\n",
			subscription.Name, message.Topic, message.Partition, message.Offset,
			string(message.Key), string(message.Value))

		return nil
	})

	if err != nil {
		logger.WithError(err).Error("multi-subscription consumer failed")
		return err
	}

	return nil
}
