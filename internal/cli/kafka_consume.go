package cli

import (
	"context"
	"fmt"
	"midas/internal/client"
	"midas/internal/config"
	"midas/internal/pkg/ctxx"
	"midas/internal/pkg/logx"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"github.com/spf13/cobra"
)

var (
	consumeTopic          string
	consumeBrokers        string
	consumeGroupID        string
	consumeBackoffMin     time.Duration
	consumeBackoffMax     time.Duration
	consumeMaxConcurrency int
)

var kafkaConsumeCmd = &cobra.Command{
	Use:   "kafka-consume",
	Short: "消费 Kafka 消息（demo）",
	RunE: func(cmd *cobra.Command, args []string) error {
		base := ctxx.WithBothIDs(cmd.Context())
		ctx, cancel := signal.NotifyContext(base, os.Interrupt, syscall.SIGTERM)
		defer cancel()

		cfg := config.Get()
		if cfg == nil {
			return fmt.Errorf("config not loaded")
		}

		brokers := []string{}
		if strings.TrimSpace(consumeBrokers) != "" {
			for _, b := range strings.Split(consumeBrokers, ",") {
				b = strings.TrimSpace(b)
				if b != "" {
					brokers = append(brokers, b)
				}
			}
		}
		if len(brokers) == 0 {
			brokers = cfg.Kafka.Brokers
		}
		if len(brokers) == 0 {
			return fmt.Errorf("no kafka brokers configured; set [kafka].brokers or --brokers")
		}

		topic := consumeTopic
		if topic == "" {
			return fmt.Errorf("no kafka topic provided; use --topic")
		}

		groupID := consumeGroupID
		if groupID == "" {
			return fmt.Errorf("no kafka group_id provided; use --group")
		}

		k := client.GetKafka()
		if k == nil {
			return fmt.Errorf("kafka client is not initialized")
		}

		// 使用新的简洁 API
		return k.ConsumeWithHandler(ctx, topic, groupID, func(ctx2 context.Context, m client.Message) error {
			// 输出到控制台
			fmt.Printf("[KAFKA] Received message - Partition: %d, Offset: %d, Key: %s, Value: %s\n",
				m.Partition, m.Offset, string(m.Key), string(m.Value))

			// 同时记录到日志
			logx.WithContext(ctx2).WithFields(map[string]any{
				"partition": m.Partition,
				"offset":    m.Offset,
				"key":       string(m.Key),
				"value":     string(m.Value),
			}).Info("received message")
			return nil
		})
	},
}

func init() {
	kafkaConsumeCmd.Flags().StringVar(&consumeTopic, "topic", "", "Kafka 主题（默认读取配置）")
	kafkaConsumeCmd.Flags().StringVar(&consumeBrokers, "brokers", "", "Kafka broker 列表，逗号分隔（默认读取配置）")
	kafkaConsumeCmd.Flags().StringVar(&consumeGroupID, "group", "", "消费组ID（默认读取配置，必填其一）")
	kafkaConsumeCmd.Flags().DurationVar(&consumeBackoffMin, "backoff-min", 500*time.Millisecond, "重连退避最小值")
	kafkaConsumeCmd.Flags().DurationVar(&consumeBackoffMax, "backoff-max", 10*time.Second, "重连退避最大值")
	kafkaConsumeCmd.Flags().IntVar(&consumeMaxConcurrency, "max-concurrency", 1, "最大并发（跨分区限流，分区内串行）")
}
