package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"

	"gorm.io/gorm"
)

// JSONMap 用于存储JSON格式的参数
type JSONMap map[string]interface{}

// Value 实现driver.Valuer接口
func (j JSONMap) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// Scan 实现sql.Scanner接口
func (j *JSONMap) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, j)
}

// RefundPolicy 退款策略模型
// 对应PHP的CohortRefundPolicy模型
type RefundPolicy struct {
	ID          *uint      `gorm:"primaryKey" json:"id"`
	Name        *string    `gorm:"column:name" json:"name"`
	Description *string    `gorm:"column:description" json:"description"`
	ParseMode   string     `gorm:"column:parse_mode" json:"parse_mode"` // hardcoded, rp_control
	RP1Percent  *int       `gorm:"column:rp_1_percent" json:"rp_1_percent"`
	RP2Percent  *int       `gorm:"column:rp_2_percent" json:"rp_2_percent"`
	RP3Percent  *int       `gorm:"column:rp_3_percent" json:"rp_3_percent"`
	RP1Params   *JSONMap   `gorm:"column:rp_1_params;type:json" json:"rp_1_params"`
	RP2Params   *JSONMap   `gorm:"column:rp_2_params;type:json" json:"rp_2_params"`
	RP3Params   *JSONMap   `gorm:"column:rp_3_params;type:json" json:"rp_3_params"`
	Remarks     *string    `gorm:"column:remarks" json:"remarks"`
	CreatedAt   *time.Time `gorm:"column:created_at" json:"created_at"`
	UpdatedAt   *time.Time `gorm:"column:updated_at" json:"updated_at"`
}

// TableName 指定表名
func (RefundPolicy) TableName() string {
	return "refund_policies"
}

// BeforeCreate GORM回调，创建前设置时间
func (r *RefundPolicy) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	r.CreatedAt = &now
	r.UpdatedAt = &now
	return nil
}

// BeforeUpdate GORM回调，更新前设置时间
func (r *RefundPolicy) BeforeUpdate(tx *gorm.DB) error {
	now := time.Now()
	r.UpdatedAt = &now
	return nil
}

// ConsumptionInfo 消费信息结构
type ConsumptionInfo struct {
	AccountTenure            int     `json:"accountTenure"`
	ConsumptionStatus        int     `json:"consumptionStatus"`
	CustomerConsented        bool    `json:"customerConsented"`
	DeliveryStatus           int     `json:"deliveryStatus"`
	LifetimeDollarsPurchased int     `json:"lifetimeDollarsPurchased"`
	LifetimeDollarsRefunded  int     `json:"lifetimeDollarsRefunded"`
	Platform                 int     `json:"platform"`
	PlayTime                 int     `json:"playTime"`
	RefundPreference         int     `json:"refundPreference"`
	SampleContentProvided    bool    `json:"sampleContentProvided"`
	UserStatus               int     `json:"userStatus"`
	AppAccountToken          string  `json:"appAccountToken"`
	DownloadTime             *string `json:"downloadTime,omitempty"`
	DiffInMinutes            *int    `json:"diffInMinutes,omitempty"`
	DiffInHours              *int    `json:"diffInHours,omitempty"`
	DiffInDays               *int    `json:"diffInDays,omitempty"`
}

// OrderInfo 订单信息结构
type OrderInfo struct {
	RegionID          *string    `json:"regionId"`
	TransactionID     string     `json:"transactionId"`
	SkuDisplayName    *string    `json:"skuDisplayName"`
	SkuPrice          *float64   `json:"skuPrice"`
	OrderTime         *time.Time `json:"orderTime"`
	RefundSuccessTime *time.Time `json:"refundSuccessTime"`
}

// RefundInfo 退款信息结构
type RefundInfo struct {
	PolicyID        int             `json:"policyId"`
	OrderInfo       OrderInfo       `json:"orderInfo"`
	ConsumptionInfo ConsumptionInfo `json:"consumptionInfo"`
}

// ComputeRefundInfo computes RefundInfo based on policy. This is a minimal
// port of the legacy PHP logic with graceful fallbacks when order data isn't available.
// In the absence of order data, this returns defaults similar to the PHP
// implementation when no order is found.
func ComputeRefundInfo(policy *RefundPolicy, adamID int64, transactionID string, appAccountToken string) RefundInfo {
	// Default order info when we cannot resolve order data in this repo
	defaultOrder := OrderInfo{
		RegionID:          nil,
		TransactionID:     transactionID,
		SkuDisplayName:    nil,
		SkuPrice:          nil,
		OrderTime:         nil,
		RefundSuccessTime: nil,
	}

	// Base consumption defaults (when no order context is available)
	baseConsumption := ConsumptionInfo{
		AccountTenure:            0,
		ConsumptionStatus:        0,
		CustomerConsented:        true,
		DeliveryStatus:           0,
		LifetimeDollarsPurchased: 0,
		LifetimeDollarsRefunded:  0,
		Platform:                 1,
		PlayTime:                 0,
		RefundPreference:         2,
		SampleContentProvided:    false,
		UserStatus:               0,
		AppAccountToken:          appAccountToken,
		DownloadTime:             nil,
		DiffInMinutes:            nil,
		DiffInHours:              nil,
		DiffInDays:               nil,
	}

	if policy == nil {
		return RefundInfo{PolicyID: 0, OrderInfo: defaultOrder, ConsumptionInfo: baseConsumption}
	}

	// rp_control mode picks a refundPreference by configured percentages and overrides fields
	if policy.ParseMode == "rp_control" {
		selectedRP := pickRefundPreference(policy)
		consumption := baseConsumption
		// Apply overrides from rp_X_params if present
		if selectedRP == 1 && policy.RP1Params != nil {
			applyOverrides(&consumption, *policy.RP1Params)
		}
		if selectedRP == 2 && policy.RP2Params != nil {
			applyOverrides(&consumption, *policy.RP2Params)
		}
		if selectedRP == 3 && policy.RP3Params != nil {
			applyOverrides(&consumption, *policy.RP3Params)
		}
		consumption.RefundPreference = selectedRP
		return RefundInfo{PolicyID: int(derefUint(policy.ID)), OrderInfo: defaultOrder, ConsumptionInfo: consumption}
	}

	// hardcoded mode
	consumption := baseConsumption
	switch int(derefUint(policy.ID)) {
	case 1:
		// keep defaults, refundPreference=2
		consumption.RefundPreference = 2
	case 2:
		consumption.RefundPreference = 2
		consumption.PlayTime = 0
	case 3:
		consumption.RefundPreference = 2
		consumption.PlayTime = 0
		consumption.ConsumptionStatus = 3
	case 4:
		consumption.RefundPreference = 3
	case 5:
		consumption.RefundPreference = 3
		consumption.PlayTime = 0
	case 6:
		// dynamic by playTime in legacy; no order context here, fallback to neutral
		consumption.RefundPreference = 3
	default:
		// unknown policy id -> keep defaults
	}
	return RefundInfo{PolicyID: int(derefUint(policy.ID)), OrderInfo: defaultOrder, ConsumptionInfo: consumption}
}

func pickRefundPreference(policy *RefundPolicy) int {
	rp1 := 0
	if policy.RP1Percent != nil {
		rp1 = *policy.RP1Percent
	}
	rp2 := 0
	if policy.RP2Percent != nil {
		rp2 = *policy.RP2Percent
	}
	rp3 := 0
	if policy.RP3Percent != nil {
		rp3 = *policy.RP3Percent
	}
	total := rp1 + rp2 + rp3
	if total <= 0 {
		return 3
	}
	// simple cumulative selection
	r := time.Now().UnixNano()%100 + 1 // 1..100 pseudo-random
	acc := int(r % 100)
	if acc <= rp1 {
		return 1
	}
	if acc <= rp1+rp2 {
		return 2
	}
	return 3
}

func applyOverrides(c *ConsumptionInfo, overrides JSONMap) {
	// Apply known fields if present and type matches
	if v, ok := overrides["accountTenure"].(float64); ok {
		c.AccountTenure = int(v)
	}
	if v, ok := overrides["consumptionStatus"].(float64); ok {
		c.ConsumptionStatus = int(v)
	}
	if v, ok := overrides["customerConsented"].(bool); ok {
		c.CustomerConsented = v
	}
	if v, ok := overrides["deliveryStatus"].(float64); ok {
		c.DeliveryStatus = int(v)
	}
	if v, ok := overrides["lifetimeDollarsPurchased"].(float64); ok {
		c.LifetimeDollarsPurchased = int(v)
	}
	if v, ok := overrides["lifetimeDollarsRefunded"].(float64); ok {
		c.LifetimeDollarsRefunded = int(v)
	}
	if v, ok := overrides["platform"].(float64); ok {
		c.Platform = int(v)
	}
	if v, ok := overrides["playTime"].(float64); ok {
		c.PlayTime = int(v)
	}
	if v, ok := overrides["refundPreference"].(float64); ok {
		c.RefundPreference = int(v)
	}
	if v, ok := overrides["sampleContentProvided"].(bool); ok {
		c.SampleContentProvided = v
	}
	if v, ok := overrides["userStatus"].(float64); ok {
		c.UserStatus = int(v)
	}
}

func derefUint(p *uint) uint {
	if p == nil {
		return 0
	}
	return *p
}
