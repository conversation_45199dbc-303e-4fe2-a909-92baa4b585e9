package models

import (
	"time"

	"gorm.io/gorm"
)

// Order 订单模型
// 对应统一订单表all_app_orders，按adam_id分区，包含63个业务字段
type Order struct {
	// 复合主键
	AdamID        int64  `gorm:"primaryKey;column:adam_id;comment:App标识，分区字段" json:"adam_id"`
	TransactionID string `gorm:"primaryKey;column:transaction_id;comment:苹果交易ID，业务主键" json:"transaction_id"`

	// 基础标识字段
	OriginalID                 int64   `gorm:"column:original_id;comment:原app_{adam_id}_orders表的自增ID，用于验证" json:"original_id"`
	AccountID                  *int64  `gorm:"column:account_id;comment:账户ID" json:"account_id"`
	RegionID                   *string `gorm:"column:region_id;comment:地区ID" json:"region_id"`
	ASARegionID                *string `gorm:"column:asa_region_id;comment:ASA地区ID" json:"asa_region_id"`
	FirstOriginalTransactionID *string `gorm:"column:first_original_transaction_id;comment:首次原始交易ID" json:"first_original_transaction_id"`
	OrderIndex                 *int    `gorm:"column:order_index;comment:订单索引" json:"order_index"`

	// 时间字段
	OrderDate   *time.Time `gorm:"column:order_date;comment:订单日期" json:"order_date"`
	OrderTime   *time.Time `gorm:"column:order_time;comment:订单时间" json:"order_time"`
	ExpiresDate *time.Time `gorm:"column:expires_date;comment:过期日期" json:"expires_date"`
	ExpiresTime *time.Time `gorm:"column:expires_time;comment:过期时间" json:"expires_time"`

	// SKU 相关字段
	SKUID          *string `gorm:"column:sku_id;comment:SKU ID" json:"sku_id"`
	SKUDisplayName *string `gorm:"column:sku_display_name;comment:SKU显示名称" json:"sku_display_name"`
	SKUType        *string `gorm:"column:sku_type;comment:SKU类型" json:"sku_type"`
	SubGroupID     *string `gorm:"column:sub_group_id;comment:订阅组ID" json:"sub_group_id"`

	// 价格和收益字段
	Price               *float64 `gorm:"column:price;comment:价格" json:"price"`
	IntroOfferPrice     *float64 `gorm:"column:intro_offer_price;comment:介绍优惠价格" json:"intro_offer_price"`
	Sales               *float64 `gorm:"column:sales;comment:销售额" json:"sales"`
	Refunds             *float64 `gorm:"column:refunds;comment:退款额" json:"refunds"`
	Proceeds            *float64 `gorm:"column:proceeds;comment:收益" json:"proceeds"`
	Income              *float64 `gorm:"column:income;comment:收入" json:"income"`
	AppleCommissionRate *float64 `gorm:"column:apple_commission_rate;comment:苹果佣金率" json:"apple_commission_rate"`
	ExtraCommissionRate *float64 `gorm:"column:extra_commission_rate;comment:额外佣金率" json:"extra_commission_rate"`
	TotalCommissionRate *float64 `gorm:"column:total_commission_rate;comment:总佣金率" json:"total_commission_rate"`

	// 状态标识字段
	IsPaying               bool `gorm:"column:is_paying;default:0;comment:是否付费" json:"is_paying"`
	NthPaying              *int `gorm:"column:nth_paying;comment:第N次付费" json:"nth_paying"`
	IsRefunded             bool `gorm:"column:is_refunded;default:0;comment:是否退款" json:"is_refunded"`
	IsCancelled            bool `gorm:"column:is_cancelled;default:0;comment:是否取消" json:"is_cancelled"`
	IsGoods                bool `gorm:"column:is_goods;default:0;comment:是否商品" json:"is_goods"`
	IsSub                  bool `gorm:"column:is_sub;default:0;comment:是否订阅" json:"is_sub"`
	IsLifetimeSub          bool `gorm:"column:is_lifetime_sub;default:0;comment:是否终身订阅" json:"is_lifetime_sub"`
	IsDirect               bool `gorm:"column:is_direct;default:0;comment:是否直接付费" json:"is_direct"`
	NthDirect              *int `gorm:"column:nth_direct;comment:第N次直接付费" json:"nth_direct"`
	IsTrial                bool `gorm:"column:is_trial;default:0;comment:是否试用" json:"is_trial"`
	IsTrialConverted       bool `gorm:"column:is_trial_converted;default:0;comment:是否试用转化" json:"is_trial_converted"`
	NthTrialConverted      *int `gorm:"column:nth_trial_converted;comment:第N次试用转化" json:"nth_trial_converted"`
	IsIntroOffer           bool `gorm:"column:is_intro_offer;default:0;comment:是否介绍优惠" json:"is_intro_offer"`
	IsIntroOfferConverted  bool `gorm:"column:is_intro_offer_converted;default:0;comment:是否介绍优惠转化" json:"is_intro_offer_converted"`
	NthIntroOfferConverted *int `gorm:"column:nth_intro_offer_converted;comment:第N次介绍优惠转化" json:"nth_intro_offer_converted"`
	IsBillingError         bool `gorm:"column:is_billing_error;default:0;comment:是否计费错误" json:"is_billing_error"`

	// 下载相关字段
	DownloadDate                *time.Time `gorm:"column:download_date;comment:下载日期" json:"download_date"`
	DownloadTime                *time.Time `gorm:"column:download_time;comment:下载时间" json:"download_time"`
	SecondsSinceDownload        *int       `gorm:"column:seconds_since_download;comment:下载后秒数" json:"seconds_since_download"`
	MinutesSinceDownload        *int       `gorm:"column:minutes_since_download;comment:下载后分钟数" json:"minutes_since_download"`
	HoursSinceDownload          *int       `gorm:"column:hours_since_download;comment:下载后小时数" json:"hours_since_download"`
	DaysSinceDownload           *int       `gorm:"column:days_since_download;comment:下载后天数" json:"days_since_download"`
	FirstOrderDaysSinceDownload *int       `gorm:"column:first_order_days_since_download;comment:首次订单距下载天数" json:"first_order_days_since_download"`

	// 取消相关字段
	CancelDate              *time.Time `gorm:"column:cancel_date;comment:取消日期" json:"cancel_date"`
	CancelTime              *time.Time `gorm:"column:cancel_time;comment:取消时间" json:"cancel_time"`
	CancelSecondsSinceOrder *int       `gorm:"column:cancel_seconds_since_order;comment:订单后取消秒数" json:"cancel_seconds_since_order"`
	CancelMinutesSinceOrder *int       `gorm:"column:cancel_minutes_since_order;comment:订单后取消分钟数" json:"cancel_minutes_since_order"`
	CancelHoursSinceOrder   *int       `gorm:"column:cancel_hours_since_order;comment:订单后取消小时数" json:"cancel_hours_since_order"`
	CancelDaysSinceOrder    *int       `gorm:"column:cancel_days_since_order;comment:订单后取消天数" json:"cancel_days_since_order"`

	// 退款相关字段
	RefundDate           *time.Time `gorm:"column:refund_date;comment:退款日期" json:"refund_date"`
	RefundTime           *time.Time `gorm:"column:refund_time;comment:退款时间" json:"refund_time"`
	RefundDaysSinceOrder *int       `gorm:"column:refund_days_since_order;comment:订单后退款天数" json:"refund_days_since_order"`

	// 广告相关字段
	OrgID      *int64 `gorm:"column:org_id;comment:组织ID" json:"org_id"`
	CampaignID *int64 `gorm:"column:campaign_id;comment:广告系列ID" json:"campaign_id"`
	AdgroupID  *int64 `gorm:"column:adgroup_id;comment:广告组ID" json:"adgroup_id"`
	KeywordID  *int64 `gorm:"column:keyword_id;comment:关键词ID" json:"keyword_id"`
	AdID       *int64 `gorm:"column:ad_id;comment:广告ID" json:"ad_id"`

	// 时间戳字段
	CreatedAt *time.Time `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt *time.Time `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
}

// TableName 指定表名
func (Order) TableName() string {
	return "all_app_orders"
}

// BeforeCreate GORM回调，创建前设置时间
func (o *Order) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	if o.CreatedAt == nil {
		o.CreatedAt = &now
	}
	if o.UpdatedAt == nil {
		o.UpdatedAt = &now
	}
	return nil
}

// BeforeUpdate GORM回调，更新前设置时间
func (o *Order) BeforeUpdate(tx *gorm.DB) error {
	now := time.Now()
	o.UpdatedAt = &now
	return nil
}

// ForApp 根据AdamID查询订单的作用域方法
func ForApp(db *gorm.DB, adamID int64) *gorm.DB {
	return db.Where("adam_id = ?", adamID)
}

// GetOrderByTransactionID 根据交易ID查找订单
func GetOrderByTransactionID(db *gorm.DB, adamID int64, transactionID string) (*Order, error) {
	var order Order
	err := ForApp(db, adamID).
		Where("transaction_id = ?", transactionID).
		First(&order).Error
	if err != nil {
		return nil, err
	}
	return &order, nil
}

// CalculateOrderToRefundRequestHours 计算订单到退款请求的小时数
func (o *Order) CalculateOrderToRefundRequestHours(refundRequestTime time.Time) float64 {
	if o.OrderTime == nil {
		return 0
	}

	duration := refundRequestTime.Sub(*o.OrderTime)
	hours := duration.Hours()

	// 保留1位小数
	return float64(int(hours*10+0.5)) / 10
}

// GetDisplayPrice 获取显示价格
func (o *Order) GetDisplayPrice() float64 {
	if o.Price == nil {
		return 0
	}
	return *o.Price
}

// GetRegionInfo 获取地区信息
func (o *Order) GetRegionInfo() (regionID string, asaRegionID string) {
	if o.RegionID != nil {
		regionID = *o.RegionID
	}

	if o.ASARegionID != nil {
		asaRegionID = *o.ASARegionID
	}

	return
}

// GetSalesAndRefunds 获取销售额和退款额
func (o *Order) GetSalesAndRefunds() (sales, refunds float64) {
	if o.Sales != nil {
		sales = *o.Sales
	}
	if o.Refunds != nil {
		refunds = *o.Refunds
	}
	return
}

// GetNetRevenue 获取净收入（销售额减去退款额）
func (o *Order) GetNetRevenue() float64 {
	sales, refunds := o.GetSalesAndRefunds()
	return sales - refunds
}

// IsPayingOrder 判断是否为付费订单
func (o *Order) IsPayingOrder() bool {
	return o.IsPaying
}

// IsSubscriptionOrder 判断是否为订阅订单
func (o *Order) IsSubscriptionOrder() bool {
	return o.IsSub
}

// IsTrialOrder 判断是否为试用订单
func (o *Order) IsTrialOrder() bool {
	return o.IsTrial
}

// IsRefundedOrder 判断是否已退款
func (o *Order) IsRefundedOrder() bool {
	return o.IsRefunded
}

// GetOrderDaysSinceDownload 获取订单距离下载的天数
func (o *Order) GetOrderDaysSinceDownload() int {
	if o.DaysSinceDownload != nil {
		return *o.DaysSinceDownload
	}
	return 0
}

// HasCampaignInfo 判断是否有广告系列信息
func (o *Order) HasCampaignInfo() bool {
	return o.CampaignID != nil && *o.CampaignID > 0
}
