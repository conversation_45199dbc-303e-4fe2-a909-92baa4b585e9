package models

import (
	"time"

	"gorm.io/gorm"
)

// AppleServerNotification Apple服务器通知模型
// 对应各App数据库中的apple_server_notifications表
type AppleServerNotification struct {
	ID                       uint       `gorm:"primaryKey" json:"id"`
	NotificationUUID         *string    `gorm:"column:notification_uuid" json:"notification_uuid"`
	NotificationType         *string    `gorm:"column:notification_type" json:"notification_type"`
	Subtype                  *string    `gorm:"column:subtype" json:"subtype"`
	NotificationTypeV2       *string    `gorm:"column:notification_type_v2" json:"notification_type_v2"`
	Summary                  *string    `gorm:"column:summary" json:"summary"`
	Version                  *string    `gorm:"column:version" json:"version"`
	Data                     *string    `gorm:"column:data" json:"data"`
	Environment              *string    `gorm:"column:environment" json:"environment"`
	AppAppleID               *uint      `gorm:"column:app_apple_id" json:"app_apple_id"`
	BundleID                 *string    `gorm:"column:bundle_id" json:"bundle_id"`
	BundleVersion            *string    `gorm:"column:bundle_version" json:"bundle_version"`
	SignedDate               *time.Time `gorm:"column:signed_date" json:"signed_date"`
	ReceiveDate              *time.Time `gorm:"column:receive_date" json:"receive_date"`
	RequestContent           *string    `gorm:"column:request_content" json:"request_content"`
	DecodedContent           *string    `gorm:"column:decoded_content" json:"decoded_content"`
	OriginalTransactionID    *string    `gorm:"column:original_transaction_id" json:"original_transaction_id"`
	TransactionID            *string    `gorm:"column:transaction_id" json:"transaction_id"`
	WebOrderLineItemID       *string    `gorm:"column:web_order_line_item_id" json:"web_order_line_item_id"`
	ProductID                *string    `gorm:"column:product_id" json:"product_id"`
	SubscriptionGroupID      *string    `gorm:"column:subscription_group_id" json:"subscription_group_id"`
	PurchaseDate             *time.Time `gorm:"column:purchase_date" json:"purchase_date"`
	OriginalPurchaseDate     *time.Time `gorm:"column:original_purchase_date" json:"original_purchase_date"`
	ExpiresDate              *time.Time `gorm:"column:expires_date" json:"expires_date"`
	Quantity                 *int       `gorm:"column:quantity" json:"quantity"`
	Type                     *string    `gorm:"column:type" json:"type"`
	InAppOwnershipType       *string    `gorm:"column:in_app_ownership_type" json:"in_app_ownership_type"`
	SignedRenewalInfo        *string    `gorm:"column:signed_renewal_info" json:"signed_renewal_info"`
	SignedTransactionInfo    *string    `gorm:"column:signed_transaction_info" json:"signed_transaction_info"`
	Status                   *int       `gorm:"column:status" json:"status"`
	ConsumptionRequestReason *string    `gorm:"column:consumption_request_reason" json:"consumption_request_reason"`
	CreatedAt                time.Time  `gorm:"column:created_at" json:"created_at"`
	UpdatedAt                time.Time  `gorm:"column:updated_at" json:"updated_at"`
}

// TableName 指定表名
func (AppleServerNotification) TableName() string {
	return "apple_server_notifications"
}

// BeforeCreate GORM回调，创建前设置时间
func (a *AppleServerNotification) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	a.CreatedAt = now
	a.UpdatedAt = now
	return nil
}

// BeforeUpdate GORM回调，更新前设置时间
func (a *AppleServerNotification) BeforeUpdate(tx *gorm.DB) error {
	a.UpdatedAt = time.Now()
	return nil
}

// IsRefundRelated 判断是否为退款相关通知
func (a *AppleServerNotification) IsRefundRelated() bool {
	if a.NotificationType == nil {
		return false
	}

	// 检查是否为生产环境
	if !a.IsProductionEnvironment() {
		return false
	}

	// 2.0版本支持 REFUND, REFUND_DECLINED 和 CONSUMPTION_REQUEST
	if a.Version != nil && *a.Version == "2.0" {
		return *a.NotificationType == "REFUND" ||
			*a.NotificationType == "REFUND_DECLINED" ||
			*a.NotificationType == "CONSUMPTION_REQUEST"
	}

	// 1.0版本只支持 REFUND
	return *a.NotificationType == "REFUND"
}

// IsConsumptionRequest 判断是否为消费请求通知
func (a *AppleServerNotification) IsConsumptionRequest() bool {
	if a.NotificationType == nil {
		return false
	}
	return *a.NotificationType == "CONSUMPTION_REQUEST"
}

// IsProductionEnvironment 判断是否为生产环境
func (a *AppleServerNotification) IsProductionEnvironment() bool {
	if a.Environment == nil {
		return false
	}

	// 2.0版本：Production
	// 1.0版本：PROD
	return *a.Environment == "Production" || *a.Environment == "PROD"
}

// ExtractTransactionID 从通知中提取transaction_id
func (a *AppleServerNotification) ExtractTransactionID() string {
	// 2.0版本：从decoded_content提取
	if a.Version != nil && *a.Version == "2.0" && a.DecodedContent != nil {
		// 这里需要JSON解析，在实际使用时会在业务逻辑中处理
		// 返回字段值作为备选
		if a.TransactionID != nil {
			return *a.TransactionID
		}
	}

	// 1.0版本：从request_content提取
	if a.RequestContent != nil {
		// 这里需要JSON解析，在实际使用时会在业务逻辑中处理
		// 返回字段值作为备选
		if a.TransactionID != nil {
			return *a.TransactionID
		}
		if a.OriginalTransactionID != nil {
			return *a.OriginalTransactionID
		}
	}

	// 兜底：使用表字段
	if a.OriginalTransactionID != nil {
		return *a.OriginalTransactionID
	}

	return ""
}

// ExtractRefundTime 从REFUND通知中提取退款时间
func (a *AppleServerNotification) ExtractRefundTime() *time.Time {
	// 2.0版本：从decoded_content的revocationDate提取
	if a.Version != nil && *a.Version == "2.0" && a.DecodedContent != nil {
		// 这里需要JSON解析，在实际使用时会在业务逻辑中处理
		// 暂时返回接收时间作为备选
	}

	// 1.0版本：从request_content的cancellation_date提取
	if a.RequestContent != nil {
		// 这里需要JSON解析，在实际使用时会在业务逻辑中处理
		// 暂时返回接收时间作为备选
	}

	// 兜底：使用通知接收时间
	if a.ReceiveDate != nil {
		return a.ReceiveDate
	}

	now := time.Now()
	return &now
}

// ExtractDeclineTime 从REFUND_DECLINED通知中提取拒绝时间
func (a *AppleServerNotification) ExtractDeclineTime() *time.Time {
	if a.ReceiveDate != nil {
		return a.ReceiveDate
	}

	now := time.Now()
	return &now
}
