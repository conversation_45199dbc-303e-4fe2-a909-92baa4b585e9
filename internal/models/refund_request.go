package models

import (
	"time"

	"gorm.io/gorm"
)

// RefundRequest 退款请求记录模型
// 对应PHP的CohortRefundRequest模型
type RefundRequest struct {
	ID                           int64      `gorm:"primaryKey" json:"id"`
	AppID                        *int       `gorm:"column:app_id" json:"app_id"`
	DeveloperID                  *int       `gorm:"column:developer_id" json:"developer_id"`
	AdamID                       *int64     `gorm:"column:adam_id" json:"adam_id"`
	AppName                      *string    `gorm:"column:app_name" json:"app_name"`
	TransactionID                *string    `gorm:"column:transaction_id" json:"transaction_id"`
	AccountID                    *int64     `gorm:"column:account_id" json:"account_id"`
	FirstPushTime                *time.Time `gorm:"column:first_push_time" json:"first_push_time"`
	PushCnt                      *int       `gorm:"column:push_cnt" json:"push_cnt"`
	OaSyncTime                   *time.Time `gorm:"column:oa_sync_time" json:"oa_sync_time"`
	RegionID                     *string    `gorm:"column:region_id" json:"region_id"`
	SkuDisplayName               *string    `gorm:"column:sku_display_name" json:"sku_display_name"`
	RefundReason                 *string    `gorm:"column:refund_reason" json:"refund_reason"`
	RefundPolicyID               *int       `gorm:"column:refund_policy_id" json:"refund_policy_id"`
	SchedProcessDelayHours       *int       `gorm:"column:sched_process_delay_hours" json:"sched_process_delay_hours"`
	SchedProcessTime             *time.Time `gorm:"column:sched_process_time" json:"sched_process_time"`
	ProcessTime                  *time.Time `gorm:"column:process_time" json:"process_time"`
	ProcessDelayHours            *float64   `gorm:"column:process_delay_hours" json:"process_delay_hours"`
	ProcessResult                *string    `gorm:"column:process_result" json:"process_result"`
	RefundResultTrackHours       *float64   `gorm:"column:refund_result_track_hours" json:"refund_result_track_hours"`
	RefundSuccessHours           *float64   `gorm:"column:refund_success_hours" json:"refund_success_hours"`
	RefundResult                 *string    `gorm:"column:refund_result" json:"refund_result"`
	RefundResultTrackTime        *time.Time `gorm:"column:refund_result_track_time" json:"refund_result_track_time"`
	RefundSuccessTime            *time.Time `gorm:"column:refund_success_time" json:"refund_success_time"`
	FirstRefundDeclineTime       *time.Time `gorm:"column:first_refund_decline_time" json:"first_refund_decline_time"`
	LastRefundDeclineTime        *time.Time `gorm:"column:last_refund_decline_time" json:"last_refund_decline_time"`
	ProcessResponseCode          *int       `gorm:"column:process_response_code" json:"process_response_code"`
	DownloadToRefundRequestHours *float64   `gorm:"column:download_to_refund_request_hours" json:"download_to_refund_request_hours"`
	OrderToRefundRequestHours    *float64   `gorm:"column:order_to_refund_request_hours" json:"order_to_refund_request_hours"`
	DownloadTime                 *time.Time `gorm:"column:download_time" json:"download_time"`
	OrderTime                    *time.Time `gorm:"column:order_time" json:"order_time"`
	AccountTenure                *int       `gorm:"column:account_tenure" json:"account_tenure"`
	PlayTime                     *int       `gorm:"column:play_time" json:"play_time"`
	ConsumptionStatus            *int       `gorm:"column:consumption_status" json:"consumption_status"`
	SampleContentProvided        *string    `gorm:"column:sample_content_provided" json:"sample_content_provided"`
	LifetimeDollarsPurchased     *int       `gorm:"column:lifetime_dollars_purchased" json:"lifetime_dollars_purchased"`
	LifetimeDollarsRefunded      *int       `gorm:"column:lifetime_dollars_refunded" json:"lifetime_dollars_refunded"`
	RefundPreference             *int       `gorm:"column:refund_preference" json:"refund_preference"`
	DownloadToProcessHours       *int       `gorm:"column:download_to_process_hours" json:"download_to_process_hours"`
	DownloadToProcessMinutes     *int       `gorm:"column:download_to_process_minutes" json:"download_to_process_minutes"`
	SkuPrice                     *float64   `gorm:"column:sku_price" json:"sku_price"`
	CustomerConsented            *string    `gorm:"column:customer_consented" json:"customer_consented"`
	DeliveryStatus               *int       `gorm:"column:delivery_status" json:"delivery_status"`
	Platform                     *int       `gorm:"column:platform" json:"platform"`
	UserStatus                   *int       `gorm:"column:user_status" json:"user_status"`
	ProcessResponseBody          *string    `gorm:"column:process_response_body" json:"process_response_body"`
	DownloadToProcessDays        *int       `gorm:"column:download_to_process_days" json:"download_to_process_days"`
	OriginalRecordID             *int64     `gorm:"column:original_record_id" json:"original_record_id"`
	NotificationID               *int64     `gorm:"column:notification_id" json:"notification_id"`
	TheDay                       string     `gorm:"column:the_day" json:"the_day"`
	LastPushTime                 *time.Time `gorm:"column:last_push_time" json:"last_push_time"`
	Environment                  *string    `gorm:"column:environment" json:"environment"`
	AppBundleID                  *string    `gorm:"column:app_bundle_id" json:"app_bundle_id"`
	AppAccountToken              *string    `gorm:"column:app_account_token" json:"app_account_token"`
	ProductID                    *string    `gorm:"column:product_id" json:"product_id"`
	OriginalTransactionID        *string    `gorm:"column:original_transaction_id" json:"original_transaction_id"`
	FirstOriginalTransactionID   *string    `gorm:"column:first_original_transaction_id" json:"first_original_transaction_id"`
	CreatedAt                    time.Time  `gorm:"column:created_at" json:"created_at"`
	UpdatedAt                    time.Time  `gorm:"column:updated_at" json:"updated_at"`
}

// TableName 指定表名
func (RefundRequest) TableName() string {
	return "refund_requests"
}

// BeforeCreate GORM回调，创建前设置时间
func (r *RefundRequest) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	r.CreatedAt = now
	r.UpdatedAt = now
	return nil
}

// BeforeUpdate GORM回调，更新前设置时间
func (r *RefundRequest) BeforeUpdate(tx *gorm.DB) error {
	r.UpdatedAt = time.Now()
	return nil
}
