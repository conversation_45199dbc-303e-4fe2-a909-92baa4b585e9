package models

import (
	"time"

	"gorm.io/gorm"
)

// AppConfig 应用配置模型
// 对应PHP的CohortAppConfig模型
type AppConfig struct {
	ID                                    uint       `gorm:"primaryKey" json:"id"`
	AppName                               *string    `gorm:"column:app_name" json:"app_name"`
	DeveloperID                           *int       `gorm:"column:developer_id" json:"developer_id"`
	CategoryID                            *int       `gorm:"column:category_id" json:"category_id"`
	AdamID                                *int64     `gorm:"column:adam_id" json:"adam_id"`
	BundleID                              *string    `gorm:"column:bundle_id" json:"bundle_id"`
	AltBundleID                           *string    `gorm:"column:alt_bundle_id" json:"alt_bundle_id"`
	DbConnection                          *string    `gorm:"column:db_connection" json:"db_connection"`
	DbName                                *string    `gorm:"column:db_name" json:"db_name"`
	DbStructVersion                       *string    `gorm:"column:db_struct_version" json:"db_struct_version"`
	ExpStructVersion                      *string    `gorm:"column:exp_struct_version" json:"exp_struct_version"`
	RefundRequestSupported                bool       `gorm:"column:refund_request_supported" json:"refund_request_supported"`
	RefundRequestEnabled                  bool       `gorm:"column:refund_request_enabled" json:"refund_request_enabled"`
	RefundPolicyID                        *string    `gorm:"column:refund_policy_id" json:"refund_policy_id"`
	OrderScheduledTasksEnabled            bool       `gorm:"column:order_scheduled_tasks_enabled" json:"order_scheduled_tasks_enabled"`
	MetricsScheduledTasksEnabled          bool       `gorm:"column:metrics_scheduled_tasks_enabled" json:"metrics_scheduled_tasks_enabled"`
	MaxProcessedAttributionRecordID       int64      `gorm:"column:max_processed_attribution_record_id" json:"max_processed_attribution_record_id"`
	MaxProcessedOrderDate                 *string    `gorm:"column:max_processed_order_date" json:"max_processed_order_date"`
	MaxProcessedDeviceExperimentGroupID   int64      `gorm:"column:max_processed_device_experiment_group_id" json:"max_processed_device_experiment_group_id"`
	AttributionProcessedAt                *time.Time `gorm:"column:attribution_processed_at" json:"attribution_processed_at"`
	OrderProcessedAt                      *time.Time `gorm:"column:order_processed_at" json:"order_processed_at"`
	DeviceExperimentGroupProcessedAt      *time.Time `gorm:"column:device_experiment_group_processed_at" json:"device_experiment_group_processed_at"`
	MaxProcessedMetricsDailyDate          *string    `gorm:"column:max_processed_metrics_daily_date" json:"max_processed_metrics_daily_date"`
	MaxProcessedMetricsWeeklyDate         *string    `gorm:"column:max_processed_metrics_weekly_date" json:"max_processed_metrics_weekly_date"`
	MaxProcessedMetricsMonthlyDate        *string    `gorm:"column:max_processed_metrics_monthly_date" json:"max_processed_metrics_monthly_date"`
	MetricsDailyProcessedAt               *time.Time `gorm:"column:metrics_daily_processed_at" json:"metrics_daily_processed_at"`
	MetricsWeeklyProcessedAt              *time.Time `gorm:"column:metrics_weekly_processed_at" json:"metrics_weekly_processed_at"`
	MetricsMonthlyProcessedAt             *time.Time `gorm:"column:metrics_monthly_processed_at" json:"metrics_monthly_processed_at"`
	UnifiedSyncEnabled                    bool       `gorm:"column:unified_sync_enabled" json:"unified_sync_enabled"`
	UnifiedSyncPriority                   int        `gorm:"column:unified_sync_priority" json:"unified_sync_priority"`
	OrdersLastSyncTime                    *time.Time `gorm:"column:orders_last_sync_time" json:"orders_last_sync_time"`
	OrdersLastSyncCount                   int        `gorm:"column:orders_last_sync_count" json:"orders_last_sync_count"`
	OrdersSyncStatus                      string     `gorm:"column:orders_sync_status" json:"orders_sync_status"`
	OrdersSyncErrorMessage                *string    `gorm:"column:orders_sync_error_message" json:"orders_sync_error_message"`
	OrdersSyncLockValue                   *string    `gorm:"column:orders_sync_lock_value" json:"orders_sync_lock_value"`
	OrdersSyncStartedAt                   *time.Time `gorm:"column:orders_sync_started_at" json:"orders_sync_started_at"`
	OrdersSyncCompletedAt                 *time.Time `gorm:"column:orders_sync_completed_at" json:"orders_sync_completed_at"`
	OrderAccountsLastSyncTime             *time.Time `gorm:"column:order_accounts_last_sync_time" json:"order_accounts_last_sync_time"`
	OrderAccountsLastSyncCount            int        `gorm:"column:order_accounts_last_sync_count" json:"order_accounts_last_sync_count"`
	OrderAccountsSyncStatus               string     `gorm:"column:order_accounts_sync_status" json:"order_accounts_sync_status"`
	OrderAccountsSyncErrorMessage         *string    `gorm:"column:order_accounts_sync_error_message" json:"order_accounts_sync_error_message"`
	OrderAccountsSyncLockValue            *string    `gorm:"column:order_accounts_sync_lock_value" json:"order_accounts_sync_lock_value"`
	OrderAccountsSyncStartedAt            *time.Time `gorm:"column:order_accounts_sync_started_at" json:"order_accounts_sync_started_at"`
	OrderAccountsSyncCompletedAt          *time.Time `gorm:"column:order_accounts_sync_completed_at" json:"order_accounts_sync_completed_at"`
	ExperimentGroupsLastSyncTime          *time.Time `gorm:"column:experiment_groups_last_sync_time" json:"experiment_groups_last_sync_time"`
	ExperimentGroupsLastSyncCount         int        `gorm:"column:experiment_groups_last_sync_count" json:"experiment_groups_last_sync_count"`
	ExperimentGroupsSyncStatus            string     `gorm:"column:experiment_groups_sync_status" json:"experiment_groups_sync_status"`
	ExperimentGroupsSyncErrorMessage      *string    `gorm:"column:experiment_groups_sync_error_message" json:"experiment_groups_sync_error_message"`
	ExperimentGroupsSyncLockValue         *string    `gorm:"column:experiment_groups_sync_lock_value" json:"experiment_groups_sync_lock_value"`
	ExperimentGroupsSyncStartedAt         *time.Time `gorm:"column:experiment_groups_sync_started_at" json:"experiment_groups_sync_started_at"`
	ExperimentGroupsSyncCompletedAt       *time.Time `gorm:"column:experiment_groups_sync_completed_at" json:"experiment_groups_sync_completed_at"`
	DeviceExperimentGroupLastSyncTime     *time.Time `gorm:"column:device_experiment_group_last_sync_time" json:"device_experiment_group_last_sync_time"`
	DeviceExperimentGroupLastSyncCount    int        `gorm:"column:device_experiment_group_last_sync_count" json:"device_experiment_group_last_sync_count"`
	DeviceExperimentGroupSyncStatus       string     `gorm:"column:device_experiment_group_sync_status" json:"device_experiment_group_sync_status"`
	DeviceExperimentGroupSyncErrorMessage *string    `gorm:"column:device_experiment_group_sync_error_message" json:"device_experiment_group_sync_error_message"`
	DeviceExperimentGroupSyncLockValue    *string    `gorm:"column:device_experiment_group_sync_lock_value" json:"device_experiment_group_sync_lock_value"`
	DeviceExperimentGroupSyncStartedAt    *time.Time `gorm:"column:device_experiment_group_sync_started_at" json:"device_experiment_group_sync_started_at"`
	DeviceExperimentGroupSyncCompletedAt  *time.Time `gorm:"column:device_experiment_group_sync_completed_at" json:"device_experiment_group_sync_completed_at"`
	DeviceAttributionsLastSyncTime        *time.Time `gorm:"column:device_attributions_last_sync_time" json:"device_attributions_last_sync_time"`
	DeviceAttributionsLastSyncCount       int        `gorm:"column:device_attributions_last_sync_count" json:"device_attributions_last_sync_count"`
	DeviceAttributionsSyncStatus          string     `gorm:"column:device_attributions_sync_status" json:"device_attributions_sync_status"`
	DeviceAttributionsSyncErrorMessage    *string    `gorm:"column:device_attributions_sync_error_message" json:"device_attributions_sync_error_message"`
	DeviceAttributionsSyncLockValue       *string    `gorm:"column:device_attributions_sync_lock_value" json:"device_attributions_sync_lock_value"`
	DeviceAttributionsSyncStartedAt       *time.Time `gorm:"column:device_attributions_sync_started_at" json:"device_attributions_sync_started_at"`
	DeviceAttributionsSyncCompletedAt     *time.Time `gorm:"column:device_attributions_sync_completed_at" json:"device_attributions_sync_completed_at"`
	SyncDetails                           *string    `gorm:"column:sync_details" json:"sync_details"`
	SyncUpdatedAt                         *time.Time `gorm:"column:sync_updated_at" json:"sync_updated_at"`
	CreatedAt                             *time.Time `gorm:"column:created_at" json:"created_at"`
	UpdatedAt                             *time.Time `gorm:"column:updated_at" json:"updated_at"`
	AppleNotificationsSyncEnabled         bool       `gorm:"column:apple_notifications_sync_enabled;default:0" json:"apple_notifications_sync_enabled"`
	AppleNotificationsLastSyncID          *int64     `gorm:"column:apple_notifications_last_sync_id" json:"apple_notifications_last_sync_id"`
}

// TableName 指定表名
func (AppConfig) TableName() string {
	return "app_config"
}

// BeforeCreate GORM回调，创建前设置时间
func (a *AppConfig) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	a.CreatedAt = &now
	a.UpdatedAt = &now
	return nil
}

// BeforeUpdate GORM回调，更新前设置时间
func (a *AppConfig) BeforeUpdate(tx *gorm.DB) error {
	now := time.Now()
	a.UpdatedAt = &now
	return nil
}
