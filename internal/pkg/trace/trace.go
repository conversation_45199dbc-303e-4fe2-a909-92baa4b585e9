package trace

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"net"
	"os"
	"strconv"
	"strings"
	"time"
)

// TraceSource 定义 trace_id 来源类型
type TraceSource int

const (
	// 生产环境: 00 ~ 2f (会影响用户的环境，包括压测)
	SourceApp       TraceSource = 0 // 00 - 移动应用生成
	SourceWeb       TraceSource = 1 // 01 - Web前端生成
	SourceGateway   TraceSource = 2 // 02 - 接入层/网关生成
	SourceService   TraceSource = 3 // 03 - 服务端生成
	SourceMQ        TraceSource = 4 // 04 - 消息队列生成
	SourceScheduler TraceSource = 5 // 05 - 定时任务生成
	SourceScript    TraceSource = 6 // 06 - 一次性任务生成

	// 离线环境: 20 ~ 4f (会影响用户的环境，包括压测)
	// 预留给离线处理系统使用

	// 测试环境: 40 ~ 6f (出现问题不会影响真实用户)
	// 预留给测试环境使用
)

// RunMode 运行模式
type RunMode string

const (
	ModeProduction RunMode = "prod"   // 生产模式
	ModeTest       RunMode = "test"   // 测试模式
	ModeReplay     RunMode = "replay" // 重放模式
)

// Header 常量定义
const (
	// 全链路透传 headers
	HeaderPassthroughPrefix = "X-Passthrough-"
	HeaderTraceID           = "X-Passthrough-Trace-Id"
	HeaderRunMode           = "X-Passthrough-Run-Mode"
	HeaderSourceIP          = "X-Passthrough-Source-Ip"
	HeaderSourceName        = "X-Passthrough-Source-Name"
	HeaderSourceTime        = "X-Passthrough-Source-Time"
	HeaderRetryCount        = "X-Passthrough-Retry-Count"

	// 需要重新计算的 headers
	HeaderComputePrefix       = "X-Compute-"
	HeaderComputeTimestamp    = "X-Compute-Timestamp"
	HeaderComputeParentSpanID = "X-Compute-Parent-Span-Id"
	HeaderComputeSpanID       = "X-Compute-Span-Id"
	HeaderComputeCaller       = "X-Compute-Caller"
	HeaderComputeCallerFunc   = "X-Compute-Caller-Func"
	HeaderComputeCallee       = "X-Compute-Callee"
	HeaderComputeCalleeFunc   = "X-Compute-Callee-Func"
)

// TraceContext 链路追踪上下文
type TraceContext struct {
	// 透传字段
	TraceID    string    `json:"trace_id"`
	RunMode    RunMode   `json:"run_mode"`
	SourceIP   string    `json:"source_ip"`
	SourceName string    `json:"source_name"`
	SourceTime time.Time `json:"source_time"`
	RetryCount int       `json:"retry_count"`

	// 计算字段
	SpanID       string `json:"span_id"`
	ParentSpanID string `json:"parent_span_id"`
	Caller       string `json:"caller"`
	CallerFunc   string `json:"caller_func"`
	Callee       string `json:"callee"`
	CalleeFunc   string `json:"callee_func"`

	// 自定义 headers
	Headers map[string]string `json:"headers"`
}

// contextKey 上下文键类型
type contextKey string

const (
	traceContextKey contextKey = "trace_context"
)

// GenerateTraceID 生成带来源标识的 trace_id
func GenerateTraceID(source TraceSource) string {
	// 生成14位随机数
	bytes := make([]byte, 7)
	rand.Read(bytes)
	randomPart := hex.EncodeToString(bytes)

	// 最后两位表示来源
	sourceStr := fmt.Sprintf("%02d", int(source))

	return randomPart + sourceStr
}

// GenerateSpanID 生成 span_id
func GenerateSpanID() string {
	bytes := make([]byte, 8)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// GetLocalIP 获取本机IP
func GetLocalIP() string {
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		return "unknown"
	}

	for _, addr := range addrs {
		if ipnet, ok := addr.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipnet.IP.To4() != nil {
				return ipnet.IP.String()
			}
		}
	}
	return "unknown"
}

// GetServiceName 获取服务名称
func GetServiceName() string {
	if name := os.Getenv("SERVICE_NAME"); name != "" {
		return name
	}
	if hostname, err := os.Hostname(); err == nil {
		return hostname
	}
	return "unknown"
}

// NewTraceContext 创建新的追踪上下文
func NewTraceContext(source TraceSource, mode RunMode) *TraceContext {
	return &TraceContext{
		TraceID:      GenerateTraceID(source),
		SpanID:       GenerateSpanID(),
		ParentSpanID: "",
		RunMode:      mode,
		SourceIP:     GetLocalIP(),
		SourceName:   GetServiceName(),
		SourceTime:   time.Now(),
		RetryCount:   0,
		Headers:      make(map[string]string),
	}
}

// NewChildSpan 创建子 span
func (tc *TraceContext) NewChildSpan() *TraceContext {
	child := &TraceContext{
		// 透传字段
		TraceID:    tc.TraceID,
		RunMode:    tc.RunMode,
		SourceIP:   tc.SourceIP,
		SourceName: tc.SourceName,
		SourceTime: tc.SourceTime,
		RetryCount: tc.RetryCount,

		// 计算字段 - 重新生成
		SpanID:       GenerateSpanID(),
		ParentSpanID: tc.SpanID,
		Caller:       tc.Callee,     // 当前的被调用方变成下一级的调用方
		CallerFunc:   tc.CalleeFunc, // 当前的被调用方法变成下一级的调用方法
		Callee:       "",            // 新的被调用方需要设置
		CalleeFunc:   "",            // 新的被调用方法需要设置

		Headers: make(map[string]string),
	}

	// 复制透传 headers
	for k, v := range tc.Headers {
		if strings.HasPrefix(k, HeaderPassthroughPrefix) {
			child.Headers[k] = v
		}
	}

	return child
}

// IncrementRetry 增加重试次数
func (tc *TraceContext) IncrementRetry() {
	tc.RetryCount++
}

// ToHeaders 转换为 HTTP/Kafka headers
func (tc *TraceContext) ToHeaders() map[string]string {
	headers := make(map[string]string)

	// 透传 headers
	headers[HeaderTraceID] = tc.TraceID
	headers[HeaderRunMode] = string(tc.RunMode)
	headers[HeaderSourceIP] = tc.SourceIP
	headers[HeaderSourceName] = tc.SourceName
	headers[HeaderSourceTime] = tc.SourceTime.Format(time.RFC3339Nano)
	headers[HeaderRetryCount] = strconv.Itoa(tc.RetryCount)

	// 计算 headers
	headers[HeaderComputeTimestamp] = time.Now().Format(time.RFC3339Nano)
	headers[HeaderComputeSpanID] = tc.SpanID
	if tc.ParentSpanID != "" {
		headers[HeaderComputeParentSpanID] = tc.ParentSpanID
	}
	if tc.Caller != "" {
		headers[HeaderComputeCaller] = tc.Caller
	}
	if tc.CallerFunc != "" {
		headers[HeaderComputeCallerFunc] = tc.CallerFunc
	}
	if tc.Callee != "" {
		headers[HeaderComputeCallee] = tc.Callee
	}
	if tc.CalleeFunc != "" {
		headers[HeaderComputeCalleeFunc] = tc.CalleeFunc
	}

	// 复制自定义 headers
	for k, v := range tc.Headers {
		headers[k] = v
	}

	return headers
}

// FromHeaders 从 headers 创建追踪上下文
func FromHeaders(headers map[string]string) *TraceContext {
	tc := &TraceContext{
		Headers: make(map[string]string),
	}

	// 解析透传信息
	tc.TraceID = headers[HeaderTraceID]
	tc.RunMode = RunMode(headers[HeaderRunMode])
	tc.SourceIP = headers[HeaderSourceIP]
	tc.SourceName = headers[HeaderSourceName]

	if timeStr := headers[HeaderSourceTime]; timeStr != "" {
		if t, err := time.Parse(time.RFC3339Nano, timeStr); err == nil {
			tc.SourceTime = t
		}
	}

	if retryStr := headers[HeaderRetryCount]; retryStr != "" {
		if count, err := strconv.Atoi(retryStr); err == nil {
			tc.RetryCount = count
		}
	}

	// 解析计算信息
	tc.SpanID = headers[HeaderComputeSpanID]
	tc.ParentSpanID = headers[HeaderComputeParentSpanID]
	tc.Caller = headers[HeaderComputeCaller]
	tc.CallerFunc = headers[HeaderComputeCallerFunc]
	tc.Callee = headers[HeaderComputeCallee]
	tc.CalleeFunc = headers[HeaderComputeCalleeFunc]

	// 复制所有 headers
	for k, v := range headers {
		tc.Headers[k] = v
	}

	return tc
}

// WithContext 将追踪上下文添加到 context
func WithContext(ctx context.Context, tc *TraceContext) context.Context {
	return context.WithValue(ctx, traceContextKey, tc)
}

// FromContext 从 context 获取追踪上下文
func FromContext(ctx context.Context) (*TraceContext, bool) {
	tc, ok := ctx.Value(traceContextKey).(*TraceContext)
	return tc, ok
}

// GetOrCreateTraceContext 获取或创建追踪上下文
func GetOrCreateTraceContext(ctx context.Context, source TraceSource, mode RunMode) (*TraceContext, context.Context) {
	if tc, ok := FromContext(ctx); ok {
		return tc, ctx
	}

	tc := NewTraceContext(source, mode)
	return tc, WithContext(ctx, tc)
}

// SetCallInfo 设置调用信息
func (tc *TraceContext) SetCallInfo(caller, callerFunc, callee, calleeFunc string) {
	tc.Caller = caller
	tc.CallerFunc = callerFunc
	tc.Callee = callee
	tc.CalleeFunc = calleeFunc
}

// NewSpanForCall 为新的调用创建 span
func (tc *TraceContext) NewSpanForCall(callee, calleeFunc string) *TraceContext {
	child := tc.NewChildSpan()
	child.Callee = callee
	child.CalleeFunc = calleeFunc
	return child
}

// IsPassthroughHeader 检查是否为透传 header
func IsPassthroughHeader(key string) bool {
	return strings.HasPrefix(key, HeaderPassthroughPrefix)
}

// IsComputeHeader 检查是否为计算 header
func IsComputeHeader(key string) bool {
	return strings.HasPrefix(key, HeaderComputePrefix)
}

// IsTraceHeader 检查是否为追踪相关 header
func IsTraceHeader(key string) bool {
	return IsPassthroughHeader(key) || IsComputeHeader(key)
}
