package ctxx

import (
	"context"
	"crypto/rand"
	"fmt"
)

type contextKey string

const (
	runIDKey   contextKey = "run_id"
	traceIDKey contextKey = "trace_id"
	TraceIDKey contextKey = "trace_id" // 导出的版本，供外部使用
)

// WithRunID 在上下文中添加 run_id
func WithRunID(ctx context.Context) context.Context {
	runID := generateID()
	return context.WithValue(ctx, runIDKey, runID)
}

// WithTraceID 在上下文中添加 trace_id
func WithTraceID(ctx context.Context) context.Context {
	traceID := generateID()
	return context.WithValue(ctx, traceIDKey, traceID)
}

// GetRunID 从上下文中获取 run_id
func GetRunID(ctx context.Context) string {
	if runID, ok := ctx.Value(runIDKey).(string); ok {
		return runID
	}
	return ""
}

// GetTraceID 从上下文中获取 trace_id
func GetTraceID(ctx context.Context) string {
	if traceID, ok := ctx.Value(traceIDKey).(string); ok {
		return traceID
	}
	return ""
}

// WithBothIDs 同时添加 run_id 和 trace_id
func WithBothIDs(ctx context.Context) context.Context {
	ctx = WithRunID(ctx)
	ctx = WithTraceID(ctx)
	return ctx
}

// generateID 生成唯一标识符
func generateID() string {
	bytes := make([]byte, 8)
	if _, err := rand.Read(bytes); err != nil {
		// 如果随机数生成失败，使用当前时间戳
		return fmt.Sprintf("%d", timeBasedID())
	}
	return fmt.Sprintf("%x", bytes)
}

// timeBasedID 基于时间的ID生成（备用方案）
func timeBasedID() int64 {
	// 简单的时间戳+随机数，避免引入更多依赖
	key := contextKey("temp")
	return 1000000 + int64(len(fmt.Sprintf("%p", &key)))%999999
}
