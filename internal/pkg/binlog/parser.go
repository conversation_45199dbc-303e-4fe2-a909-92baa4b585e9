package binlog

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"
)

// DebeziumMessage Debezium binlog消息结构
type DebeziumMessage struct {
	Schema  *Schema  `json:"schema"`
	Payload *Payload `json:"payload"`
}

// Schema Debezium消息的schema部分
type Schema struct {
	Type   string  `json:"type"`
	Fields []Field `json:"fields"`
	Name   string  `json:"name"`
}

// Field schema字段定义
type Field struct {
	Type     string `json:"type"`
	Optional bool   `json:"optional"`
	Field    string `json:"field"`
}

// Payload Debezium消息的payload部分
type Payload struct {
	Before      map[string]interface{} `json:"before"`
	After       map[string]interface{} `json:"after"`
	Source      *Source                `json:"source"`
	Op          string                 `json:"op"` // c=create, u=update, d=delete, r=read
	TsMs        int64                  `json:"ts_ms"`
	Transaction interface{}            `json:"transaction"`
}

// Source 数据源信息
type Source struct {
	Version   string `json:"version"`
	Connector string `json:"connector"`
	Name      string `json:"name"`
	TsMs      int64  `json:"ts_ms"`
	Snapshot  string `json:"snapshot"`
	Db        string `json:"db"`
	Sequence  string `json:"sequence"`
	Table     string `json:"table"`
	ServerID  int    `json:"server_id"`
	Gtid      string `json:"gtid"`
	File      string `json:"file"`
	Pos       int64  `json:"pos"`
	Row       int    `json:"row"`
	Thread    int    `json:"thread"`
	Query     string `json:"query"`
}

// ChangeEvent 表变更事件
type ChangeEvent struct {
	Operation string                 // 操作类型：INSERT, UPDATE, DELETE
	Database  string                 // 数据库名
	Table     string                 // 表名
	Before    map[string]interface{} // 变更前数据
	After     map[string]interface{} // 变更后数据
	Timestamp time.Time              // 变更时间
	Source    *Source                // 源信息
	Headers   map[string]string      // 消息头信息（链路追踪等）
}

// ParseDebeziumMessage 解析Debezium binlog消息
func ParseDebeziumMessage(messageValue []byte) (*ChangeEvent, error) {
	return ParseDebeziumMessageWithHeaders(messageValue, nil)
}

// ParseDebeziumMessageWithHeaders 解析Debezium binlog消息并包含headers信息
func ParseDebeziumMessageWithHeaders(messageValue []byte, headers map[string]string) (*ChangeEvent, error) {
	// 解析标准的Debezium消息格式
	var msg DebeziumMessage
	if err := json.Unmarshal(messageValue, &msg); err != nil {
		return nil, fmt.Errorf("failed to unmarshal debezium message: %w", err)
	}

	if msg.Payload == nil {
		return nil, fmt.Errorf("payload is nil")
	}

	if msg.Payload.Source == nil {
		return nil, fmt.Errorf("source is nil")
	}

	return parseStandardDebeziumMessage(&msg, headers)
}

// parseStandardDebeziumMessage 解析标准的Debezium消息格式
func parseStandardDebeziumMessage(msg *DebeziumMessage, headers map[string]string) (*ChangeEvent, error) {
	if msg.Payload == nil {
		return nil, fmt.Errorf("payload is nil")
	}

	if msg.Payload.Source == nil {
		return nil, fmt.Errorf("source is nil")
	}

	// 转换操作类型
	operation := ""
	switch msg.Payload.Op {
	case "c":
		operation = "INSERT"
	case "u":
		operation = "UPDATE"
	case "d":
		operation = "DELETE"
	case "r":
		operation = "READ" // 初始快照
	default:
		return nil, fmt.Errorf("unknown operation: %s", msg.Payload.Op)
	}

	// 转换时间戳
	timestamp := time.Unix(0, msg.Payload.TsMs*int64(time.Millisecond))

	event := &ChangeEvent{
		Operation: operation,
		Database:  msg.Payload.Source.Db,
		Table:     msg.Payload.Source.Table,
		Before:    msg.Payload.Before,
		After:     msg.Payload.After,
		Timestamp: timestamp,
		Source:    msg.Payload.Source,
		Headers:   headers,
	}

	return event, nil
}

// IsAppleServerNotification 判断是否为apple_server_notifications表的变更
func (e *ChangeEvent) IsAppleServerNotification() bool {
	return e.Table == "apple_server_notifications"
}

// IsInsertOrUpdate 判断是否为插入或更新操作
func (e *ChangeEvent) IsInsertOrUpdate() bool {
	return e.Operation == "INSERT" || e.Operation == "UPDATE"
}

// GetRecordData 获取记录数据（优先使用After，如果为空则使用Before）
func (e *ChangeEvent) GetRecordData() map[string]interface{} {
	if len(e.After) > 0 {
		return e.After
	}
	return e.Before
}

// GetStringField 从记录数据中获取字符串字段
func (e *ChangeEvent) GetStringField(fieldName string) string {
	data := e.GetRecordData()
	if data == nil {
		return ""
	}

	if value, exists := data[fieldName]; exists && value != nil {
		if str, ok := value.(string); ok {
			return str
		}
	}
	return ""
}

// GetIntField 从记录数据中获取整数字段
func (e *ChangeEvent) GetIntField(fieldName string) int {
	data := e.GetRecordData()
	if data == nil {
		return 0
	}

	if value, exists := data[fieldName]; exists && value != nil {
		switch v := value.(type) {
		case int:
			return v
		case int64:
			return int(v)
		case float64:
			return int(v)
		case string:
			if i, err := strconv.Atoi(v); err == nil {
				return i
			}
		}
	}
	return 0
}

// GetTimeField 从记录数据中获取时间字段
func (e *ChangeEvent) GetTimeField(fieldName string) *time.Time {
	data := e.GetRecordData()
	if data == nil {
		return nil
	}

	if value, exists := data[fieldName]; exists && value != nil {
		switch v := value.(type) {
		case string:
			// 尝试解析不同的时间格式
			formats := []string{
				"2006-01-02 15:04:05",
				"2006-01-02T15:04:05Z",
				"2006-01-02T15:04:05.000Z",
				time.RFC3339,
			}
			for _, format := range formats {
				if t, err := time.Parse(format, v); err == nil {
					return &t
				}
			}
		case int64:
			// Unix时间戳（秒）
			t := time.Unix(v, 0)
			return &t
		case float64:
			// Unix时间戳（可能包含毫秒）
			if v > 1e10 { // 毫秒时间戳
				t := time.Unix(0, int64(v)*int64(time.Millisecond))
				return &t
			} else { // 秒时间戳
				t := time.Unix(int64(v), 0)
				return &t
			}
		}
	}
	return nil
}

// GetBoolField 从记录数据中获取布尔字段
func (e *ChangeEvent) GetBoolField(fieldName string) bool {
	data := e.GetRecordData()
	if data == nil {
		return false
	}

	if value, exists := data[fieldName]; exists && value != nil {
		switch v := value.(type) {
		case bool:
			return v
		case int:
			return v != 0
		case string:
			return v == "true" || v == "1"
		}
	}
	return false
}

// ExtractTopicInfo 从Kafka topic名称中提取数据库和表信息
// topic格式通常为: midas.{database}.{table}
func ExtractTopicInfo(topic string) (database, table string, err error) {
	// 简单的topic解析，假设格式为 midas.{database}.{table}
	// 实际使用时可能需要更复杂的解析逻辑
	parts := make([]string, 0)
	current := ""
	for _, char := range topic {
		if char == '.' {
			if current != "" {
				parts = append(parts, current)
				current = ""
			}
		} else {
			current += string(char)
		}
	}
	if current != "" {
		parts = append(parts, current)
	}

	if len(parts) < 3 {
		return "", "", fmt.Errorf("invalid topic format: %s, expected format: midas.{database}.{table}", topic)
	}

	// 假设格式为 midas.{database}.{table}
	database = parts[1]
	table = parts[2]
	return database, table, nil
}
