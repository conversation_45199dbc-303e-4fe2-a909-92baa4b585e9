package logx

import (
	"context"
	"fmt"
	"io"
	"midas/internal/config"
	"midas/internal/pkg/ctxx"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/rs/zerolog"
)

// Logger 日志记录器接口
type Logger interface {
	WithContext(ctx context.Context) Logger
	WithFields(fields map[string]interface{}) Logger
	WithError(err error) Logger
	WithField(key string, value interface{}) Logger

	Debug(msg string)
	Info(msg string)
	Warn(msg string)
	Error(msg string)
	Fatal(msg string)
}

// zerologLogger 基于 Zerolog 的日志实现
type zerologLogger struct {
	infoLogger zerolog.Logger // 用于 debug, info 级别
	warnLogger zerolog.Logger // 用于 warn, error, fatal 级别
	fields     map[string]interface{}
}

var (
	globalLogger *zerologLogger
	initOnce     sync.Once
)

// Init 初始化日志系统
func Init() error {
	var err error
	initOnce.Do(func() {
		err = initLogger()
	})
	return err
}

// initLogger 初始化日志记录器
func initLogger() error {
	cfg := config.Get()
	if cfg == nil {
		return fmt.Errorf("config not loaded")
	}

	// 确保日志目录存在
	if err := os.MkdirAll(cfg.App.LogDir, 0755); err != nil {
		return fmt.Errorf("failed to create log directory: %w", err)
	}

	// 创建日志文件
	infoWriter, err := createLogWriter(cfg.App.LogDir, "app.log")
	if err != nil {
		return fmt.Errorf("failed to create info log writer: %w", err)
	}

	warnWriter, err := createLogWriter(cfg.App.LogDir, "app.log.wf")
	if err != nil {
		return fmt.Errorf("failed to create warn log writer: %w", err)
	}

	// 配置 Zerolog
	zerolog.TimeFieldFormat = time.RFC3339

	// 创建 info 级别的写入器
	var infoWriters []io.Writer
	if cfg.App.Env == "development" {
		// 开发环境同时输出到控制台
		infoWriters = []io.Writer{infoWriter, os.Stdout}
	} else {
		infoWriters = []io.Writer{infoWriter}
	}

	// 创建 warn 级别的写入器
	var warnWriters []io.Writer
	if cfg.App.Env == "development" {
		warnWriters = []io.Writer{warnWriter, os.Stdout}
	} else {
		warnWriters = []io.Writer{warnWriter}
	}

	infoMulti := io.MultiWriter(infoWriters...)
	warnMulti := io.MultiWriter(warnWriters...)

	infoLogger := zerolog.New(infoMulti).With().
		Timestamp().
		Logger()

	warnLogger := zerolog.New(warnMulti).With().
		Timestamp().
		Logger()

	globalLogger = &zerologLogger{
		infoLogger: infoLogger,
		warnLogger: warnLogger,
		fields:     make(map[string]interface{}),
	}

	return nil
}

// createLogWriter 创建日志写入器
func createLogWriter(logDir, filename string) (io.Writer, error) {
	// 当前小时的日志文件
	now := time.Now()
	hourSuffix := now.Format("2006010215")
	logFile := filepath.Join(logDir, fmt.Sprintf("%s.%s", filename, hourSuffix))

	file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return nil, err
	}

	return file, nil
}

// WithContext 创建带上下文的日志记录器
func WithContext(ctx context.Context) Logger {
	if globalLogger == nil {
		return &fallbackLogger{}
	}

	fields := make(map[string]interface{})
	for k, v := range globalLogger.fields {
		fields[k] = v
	}

	// 从上下文中提取信息
	if runID := ctxx.GetRunID(ctx); runID != "" {
		fields["run_id"] = runID
	}
	if traceID := ctxx.GetTraceID(ctx); traceID != "" {
		fields["trace_id"] = traceID
	}

	return &zerologLogger{
		infoLogger: globalLogger.infoLogger,
		warnLogger: globalLogger.warnLogger,
		fields:     fields,
	}
}

// WithFields 添加字段
func (l *zerologLogger) WithFields(fields map[string]interface{}) Logger {
	newFields := make(map[string]interface{})
	for k, v := range l.fields {
		newFields[k] = v
	}
	for k, v := range fields {
		newFields[k] = v
	}

	return &zerologLogger{
		infoLogger: l.infoLogger,
		warnLogger: l.warnLogger,
		fields:     newFields,
	}
}

// WithField 添加单个字段
func (l *zerologLogger) WithField(key string, value interface{}) Logger {
	return l.WithFields(map[string]interface{}{key: value})
}

// WithError 添加错误字段
func (l *zerologLogger) WithError(err error) Logger {
	return l.WithField("error", err.Error())
}

// WithContext 返回自身（实现接口）
func (l *zerologLogger) WithContext(ctx context.Context) Logger {
	return WithContext(ctx)
}

// Debug 记录调试日志
func (l *zerologLogger) Debug(msg string) {
	event := l.infoLogger.Debug()
	for k, v := range l.fields {
		event = event.Interface(k, v)
	}
	event.Msg(msg)
}

// Info 记录信息日志
func (l *zerologLogger) Info(msg string) {
	event := l.infoLogger.Info()
	for k, v := range l.fields {
		event = event.Interface(k, v)
	}
	event.Msg(msg)
}

// Warn 记录警告日志
func (l *zerologLogger) Warn(msg string) {
	event := l.warnLogger.Warn()
	for k, v := range l.fields {
		event = event.Interface(k, v)
	}
	event.Msg(msg)
}

// Error 记录错误日志
func (l *zerologLogger) Error(msg string) {
	event := l.warnLogger.Error()
	for k, v := range l.fields {
		event = event.Interface(k, v)
	}
	event.Msg(msg)
}

// Fatal 记录致命错误日志
func (l *zerologLogger) Fatal(msg string) {
	event := l.warnLogger.Fatal()
	for k, v := range l.fields {
		event = event.Interface(k, v)
	}
	event.Msg(msg)
}

// fallbackLogger 备用日志记录器（当主日志系统未初始化时使用）
type fallbackLogger struct{}

func (f *fallbackLogger) WithContext(ctx context.Context) Logger          { return f }
func (f *fallbackLogger) WithFields(fields map[string]interface{}) Logger { return f }
func (f *fallbackLogger) WithError(err error) Logger                      { return f }
func (f *fallbackLogger) WithField(key string, value interface{}) Logger  { return f }
func (f *fallbackLogger) Debug(msg string)                                { fmt.Printf("[DEBUG] %s\n", msg) }
func (f *fallbackLogger) Info(msg string)                                 { fmt.Printf("[INFO] %s\n", msg) }
func (f *fallbackLogger) Warn(msg string)                                 { fmt.Printf("[WARN] %s\n", msg) }
func (f *fallbackLogger) Error(msg string)                                { fmt.Printf("[ERROR] %s\n", msg) }
func (f *fallbackLogger) Fatal(msg string)                                { fmt.Printf("[FATAL] %s\n", msg); os.Exit(1) }
