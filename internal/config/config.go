package config

import (
	"fmt"
	"time"

	"github.com/spf13/viper"
)

// Config 全局配置结构
type Config struct {
	App    AppConfig    `toml:"app" mapstructure:"app"`
	HTTP   HTTPConfig   `toml:"http" mapstructure:"http"`
	MySQL  MySQLConfig  `toml:"mysql" mapstructure:"mysql"`
	Redis  RedisConfig  `toml:"redis" mapstructure:"redis"`
	Refund RefundConfig `toml:"refund" mapstructure:"refund"`
	Apple  AppleConfig  `toml:"apple" mapstructure:"apple"`
	Kafka  KafkaConfig  `toml:"kafka" mapstructure:"kafka"`
}

// AppConfig 应用配置
type AppConfig struct {
	Name   string `toml:"name" mapstructure:"name"`
	Env    string `toml:"env" mapstructure:"env"`
	LogDir string `toml:"log_dir" mapstructure:"log_dir"`
}

// HTTPConfig HTTP客户端配置
type HTTPConfig struct {
	Timeout    string `toml:"timeout" mapstructure:"timeout"`
	RetryCount int    `toml:"retry_count" mapstructure:"retry_count"`
	RetryWait  string `toml:"retry_wait" mapstructure:"retry_wait"`
}

// MySQLConfig MySQL数据库配置
type MySQLConfig struct {
	DSN             string `toml:"dsn" mapstructure:"dsn"`
	MaxIdle         int    `toml:"max_idle" mapstructure:"max_idle"`
	MaxOpen         int    `toml:"max_open" mapstructure:"max_open"`
	ConnMaxLifetime string `toml:"conn_max_lifetime" mapstructure:"conn_max_lifetime"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Addr     string `toml:"addr" mapstructure:"addr"`
	Password string `toml:"password" mapstructure:"password"`
	DB       int    `toml:"db" mapstructure:"db"`
	Username string `toml:"username" mapstructure:"username"`
	PoolSize int    `toml:"pool_size" mapstructure:"pool_size"`
}

// RefundConfig 退款处理配置
type RefundConfig struct {
	DefaultPolicyID        int   `toml:"default_policy_id" mapstructure:"default_policy_id"`
	SchedProcessDelayHours []int `toml:"sched_process_delay_hours" mapstructure:"sched_process_delay_hours"`
	MaxProcessingDays      int   `toml:"max_processing_days" mapstructure:"max_processing_days"`
}

// AppleConfig Apple相关配置
type AppleConfig struct {
	Timeout string `toml:"timeout" mapstructure:"timeout"`
}

// KafkaConfig Kafka相关配置
type KafkaConfig struct {
	Brokers []string `toml:"brokers" mapstructure:"brokers"`
	// 多订阅配置
	Subscriptions []KafkaSubscription `toml:"subscriptions" mapstructure:"subscriptions"`
}

// KafkaSubscription Kafka订阅配置
type KafkaSubscription struct {
	Name        string   `toml:"name" mapstructure:"name"`               // 订阅名称
	GroupID     string   `toml:"group_id" mapstructure:"group_id"`       // 消费组ID
	Topics      []string `toml:"topics" mapstructure:"topics"`           // 主题列表
	Enabled     bool     `toml:"enabled" mapstructure:"enabled"`         // 是否启用
	Description string   `toml:"description" mapstructure:"description"` // 描述
}

var (
	globalConfig *Config
)

// Load 加载配置文件
func Load(configFile string) error {
	viper.SetConfigFile(configFile)
	viper.SetConfigType("toml")

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		return fmt.Errorf("failed to read config file: %w", err)
	}

	// 解析配置
	var cfg Config
	if err := viper.Unmarshal(&cfg); err != nil {
		return fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// 验证必要配置
	if err := validate(&cfg); err != nil {
		return fmt.Errorf("config validation failed: %w", err)
	}

	globalConfig = &cfg
	return nil
}

// Get 获取全局配置
func Get() *Config {
	return globalConfig
}

// validate 验证配置
func validate(cfg *Config) error {
	if cfg.App.Name == "" {
		return fmt.Errorf("app.name is required")
	}

	if cfg.App.LogDir == "" {
		return fmt.Errorf("app.log_dir is required")
	}

	// 解析时间配置以验证格式
	if cfg.HTTP.Timeout != "" {
		if _, err := time.ParseDuration(cfg.HTTP.Timeout); err != nil {
			return fmt.Errorf("invalid http.timeout format: %w", err)
		}
	}

	if cfg.HTTP.RetryWait != "" {
		if _, err := time.ParseDuration(cfg.HTTP.RetryWait); err != nil {
			return fmt.Errorf("invalid http.retry_wait format: %w", err)
		}
	}

	if cfg.MySQL.ConnMaxLifetime != "" {
		if _, err := time.ParseDuration(cfg.MySQL.ConnMaxLifetime); err != nil {
			return fmt.Errorf("invalid mysql.conn_max_lifetime format: %w", err)
		}
	}

	if cfg.Apple.Timeout != "" {
		if _, err := time.ParseDuration(cfg.Apple.Timeout); err != nil {
			return fmt.Errorf("invalid apple.timeout format: %w", err)
		}
	}

	// Kafka 配置校验（非强制，仅在填写时校验 brokers 列表）
	if len(cfg.Kafka.Brokers) > 0 {
		for i, b := range cfg.Kafka.Brokers {
			if b == "" {
				return fmt.Errorf("kafka.brokers[%d] is empty", i)
			}
		}
	}

	return nil
}
