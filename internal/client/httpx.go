package client

import (
	"context"
	"midas/internal/pkg/trace"
	"net/http"
	"time"

	"github.com/go-resty/resty/v2"
)

// HTTPClient HTTP 客户端包装器，自动处理追踪 headers
type HTTPClient struct {
	client *resty.Client
}

// NewHTTPClient 创建新的 HTTP 客户端
func NewHTTPClient() *HTTPClient {
	client := resty.New()

	// 设置请求中间件，自动注入追踪 headers
	client.OnBeforeRequest(func(c *resty.Client, req *resty.Request) error {
		ctx := req.Context()
		if ctx == nil {
			ctx = context.Background()
		}

		// 获取或创建追踪上下文
		tc, newCtx := trace.GetOrCreateTraceContext(ctx, trace.SourceService, trace.ModeProduction)

		// 设置调用信息
		tc.SetCallInfo("http-client", req.Method, "http-server", req.URL)

		// 生成新的 span
		childTC := tc.NewSpanForCall("http-server", req.URL)

		// 转换为 headers
		headers := childTC.ToHeaders()

		// 添加到请求 headers
		for k, v := range headers {
			req.SetHeader(k, v)
		}

		// 更新请求上下文
		req.SetContext(newCtx)

		return nil
	})

	// 设置响应中间件，处理响应 headers
	client.OnAfterResponse(func(c *resty.Client, resp *resty.Response) error {
		// 这里可以处理响应中的追踪信息
		// 例如记录响应时间、状态码等
		return nil
	})

	return &HTTPClient{
		client: client,
	}
}

// Get 发送 GET 请求
func (h *HTTPClient) Get(ctx context.Context, url string) (*resty.Response, error) {
	return h.client.R().SetContext(ctx).Get(url)
}

// Post 发送 POST 请求
func (h *HTTPClient) Post(ctx context.Context, url string, body interface{}) (*resty.Response, error) {
	return h.client.R().SetContext(ctx).SetBody(body).Post(url)
}

// Put 发送 PUT 请求
func (h *HTTPClient) Put(ctx context.Context, url string, body interface{}) (*resty.Response, error) {
	return h.client.R().SetContext(ctx).SetBody(body).Put(url)
}

// Delete 发送 DELETE 请求
func (h *HTTPClient) Delete(ctx context.Context, url string) (*resty.Response, error) {
	return h.client.R().SetContext(ctx).Delete(url)
}

// Request 创建新的请求构建器
func (h *HTTPClient) Request(ctx context.Context) *resty.Request {
	return h.client.R().SetContext(ctx)
}

// SetTimeout 设置超时时间
func (h *HTTPClient) SetTimeout(timeout string) *HTTPClient {
	h.client.SetTimeout(parseTimeout(timeout))
	return h
}

// SetRetryCount 设置重试次数
func (h *HTTPClient) SetRetryCount(count int) *HTTPClient {
	h.client.SetRetryCount(count)
	return h
}

// SetHeader 设置默认 header
func (h *HTTPClient) SetHeader(header, value string) *HTTPClient {
	h.client.SetHeader(header, value)
	return h
}

// SetHeaders 设置多个默认 headers
func (h *HTTPClient) SetHeaders(headers map[string]string) *HTTPClient {
	h.client.SetHeaders(headers)
	return h
}

// parseTimeout 解析超时时间字符串
func parseTimeout(timeout string) time.Duration {
	if d, err := time.ParseDuration(timeout); err == nil {
		return d
	}
	return 30 * time.Second // 默认 30 秒
}

// HTTPMiddleware HTTP 中间件，用于处理传入的 HTTP 请求
func HTTPMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// 从请求 headers 中恢复追踪上下文
		headers := make(map[string]string)
		for k, v := range r.Header {
			if len(v) > 0 && trace.IsTraceHeader(k) {
				headers[k] = v[0]
			}
		}

		var tc *trace.TraceContext
		var newCtx context.Context

		if len(headers) > 0 && headers[trace.HeaderTraceID] != "" {
			// 从 headers 恢复追踪上下文
			tc = trace.FromHeaders(headers)
			newCtx = trace.WithContext(ctx, tc)
		} else {
			// 创建新的追踪上下文
			tc, newCtx = trace.GetOrCreateTraceContext(ctx, trace.SourceService, trace.ModeProduction)
		}

		// 创建处理请求的子 span
		childTC := tc.NewSpanForCall("http-handler", r.URL.Path)
		childTC.SetCallInfo("http-server", r.Method, "http-handler", r.URL.Path)
		newCtx = trace.WithContext(newCtx, childTC)

		// 将追踪信息添加到响应 headers
		responseHeaders := childTC.ToHeaders()
		for k, v := range responseHeaders {
			if trace.IsTraceHeader(k) {
				w.Header().Set(k, v)
			}
		}

		// 使用新的上下文继续处理请求
		next.ServeHTTP(w, r.WithContext(newCtx))
	})
}

var httpxClient *HTTPClient

// GetHTTPClient 获取全局 HTTP 客户端
func GetHTTPClient() *HTTPClient {
	if httpxClient == nil {
		httpxClient = NewHTTPClient()
	}
	return httpxClient
}

// initHTTP 初始化 HTTP 客户端
func initHTTP() {
	httpxClient = NewHTTPClient()
}
