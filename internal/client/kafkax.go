package client

import (
	"context"
	"fmt"
	"midas/internal/config"
	"midas/internal/pkg/ctxx"
	"midas/internal/pkg/logx"
	"midas/internal/pkg/trace"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/twmb/franz-go/pkg/kgo"
)

// Message 统一的消息结构
type Message struct {
	Key       []byte            `json:"key"`
	Value     []byte            `json:"value"`
	Headers   map[string]string `json:"headers"`
	Topic     string            `json:"topic"`
	Partition int               `json:"partition"`
	Offset    int64             `json:"offset"`
	Timestamp time.Time         `json:"timestamp"`
}

// MessageHandler 定义消息处理函数类型
// ctx 包含 trace_id 和超时控制
type MessageHandler func(ctx context.Context, message Message) error

// SubscriptionHandler 订阅处理函数类型
type SubscriptionHandler func(ctx context.Context, subscription config.KafkaSubscription, message Message) error

// KafkaOptions Kafka 客户端配置选项
type KafkaOptions struct {
	// 基础配置
	Brokers []string `json:"brokers"` // Kafka broker 地址列表

	// 生产者配置
	ProducerBatchSize    int           `json:"producer_batch_size"`    // 批量发送大小，默认 100
	ProducerBatchTimeout time.Duration `json:"producer_batch_timeout"` // 批量发送超时，默认 10ms
	ProducerRequiredAcks int           `json:"producer_required_acks"` // 确认级别，默认 1 (leader)
	ProducerRetryMax     int           `json:"producer_retry_max"`     // 重试次数，默认 3
	ProducerAsync        bool          `json:"producer_async"`         // 异步发送，默认 false

	// 消费者配置
	ConsumerMinBytes          int           `json:"consumer_min_bytes"`          // 最小读取字节数，默认 1
	ConsumerMaxBytes          int           `json:"consumer_max_bytes"`          // 最大读取字节数，默认 10MB
	ConsumerMaxWait           time.Duration `json:"consumer_max_wait"`           // 最大等待时间，默认 1s
	ConsumerCommitInterval    time.Duration `json:"consumer_commit_interval"`    // 提交间隔，默认 1s
	ConsumerStartOffset       int64         `json:"consumer_start_offset"`       // 起始偏移量，默认 FirstOffset
	ConsumerSessionTimeout    time.Duration `json:"consumer_session_timeout"`    // 会话超时，默认 30s
	ConsumerHeartbeatInterval time.Duration `json:"consumer_heartbeat_interval"` // 心跳间隔，默认 3s

	// 连接配置
	DialTimeout  time.Duration `json:"dial_timeout"`  // 连接超时，默认 10s
	ReadTimeout  time.Duration `json:"read_timeout"`  // 读取超时，默认 10s
	WriteTimeout time.Duration `json:"write_timeout"` // 写入超时，默认 10s

	// 错误处理配置
	EnableDeadLetterQueue bool          `json:"enable_dead_letter_queue"` // 是否启用死信队列，默认 false
	DeadLetterTopic       string        `json:"dead_letter_topic"`        // 死信队列主题
	MaxRetries            int           `json:"max_retries"`              // 消息处理最大重试次数，默认 3
	RetryBackoff          time.Duration `json:"retry_backoff"`            // 重试退避时间，默认 1s

	// 监控配置
	EnableMetrics bool `json:"enable_metrics"` // 是否启用指标，默认 false
	EnableAlerts  bool `json:"enable_alerts"`  // 是否启用报警，默认 false (TODO: 预留)

	// 消息处理配置
	MessageTimeout time.Duration `json:"message_timeout"` // 单条消息处理超时，默认 30s
	MaxConcurrency int           `json:"max_concurrency"` // 最大并发处理数，默认 1
}

// Kafka 提供生产级别的 Kafka 客户端封装
type Kafka struct {
	cfg     *config.Config
	options *KafkaOptions
	client  *kgo.Client
	mu      sync.RWMutex
}

var kafkaClient *Kafka

// GetKafka 返回全局 Kafka 客户端（通过 InitClients 初始化）
func GetKafka() *Kafka {
	return kafkaClient
}

// DefaultKafkaOptions 返回 CDC 场景的默认配置
func DefaultKafkaOptions() *KafkaOptions {
	return &KafkaOptions{
		// 生产者配置 - CDC 场景优化
		ProducerBatchSize:    100,
		ProducerBatchTimeout: 10 * time.Millisecond,
		ProducerRequiredAcks: 1, // leader 确认即可，平衡性能和可靠性
		ProducerRetryMax:     3,
		ProducerAsync:        false,

		// 消费者配置 - CDC 场景优化
		ConsumerMinBytes:          1,
		ConsumerMaxBytes:          10 * 1024 * 1024, // 10MB
		ConsumerMaxWait:           1 * time.Second,
		ConsumerCommitInterval:    1 * time.Second,
		ConsumerStartOffset:       -2, // FirstOffset equivalent
		ConsumerSessionTimeout:    30 * time.Second,
		ConsumerHeartbeatInterval: 3 * time.Second,

		// 连接配置
		DialTimeout:  10 * time.Second,
		ReadTimeout:  10 * time.Second,
		WriteTimeout: 10 * time.Second,

		// 错误处理配置
		EnableDeadLetterQueue: false,
		DeadLetterTopic:       "",
		MaxRetries:            3,
		RetryBackoff:          1 * time.Second,

		// 监控配置
		EnableMetrics: false,
		EnableAlerts:  false,

		// 消息处理配置
		MessageTimeout: 30 * time.Second,
		MaxConcurrency: 1,
	}
}

// NewKafka 创建新的 Kafka 客户端
func NewKafka(cfg *config.Config, options ...*KafkaOptions) *Kafka {
	var opts *KafkaOptions
	if len(options) > 0 && options[0] != nil {
		opts = options[0]
	} else {
		opts = DefaultKafkaOptions()
	}

	// 设置 brokers
	if len(opts.Brokers) == 0 {
		opts.Brokers = cfg.Kafka.Brokers
	}

	k := &Kafka{
		cfg:     cfg,
		options: opts,
	}

	// 创建 franz-go 客户端
	k.client = k.createFranzGoClient()

	return k
}

// createFranzGoClient 创建 franz-go 客户端
func (k *Kafka) createFranzGoClient() *kgo.Client {
	batchSize := k.options.ProducerBatchSize
	if batchSize < 1024 { // 确保批次大小至少为 1KB
		batchSize = 1024 * 1024 // 默认 1MB
	}

	opts := []kgo.Opt{
		kgo.SeedBrokers(k.options.Brokers...),
		kgo.ProducerBatchMaxBytes(int32(batchSize)),
		kgo.ProducerBatchCompression(kgo.GzipCompression()),
		kgo.RequiredAcks(kgo.LeaderAck()), // 使用 leader 确认
		kgo.DisableIdempotentWrite(),      // 简化配置
	}

	client, err := kgo.NewClient(opts...)
	if err != nil {
		panic(fmt.Sprintf("failed to create kafka client: %v", err))
	}

	return client
}

// initKafka 初始化全局 Kafka 客户端
func initKafka(cfg *config.Config) {
	kafkaClient = NewKafka(cfg)
}

// Close 关闭 Kafka 客户端
func (k *Kafka) Close() error {
	k.mu.Lock()
	defer k.mu.Unlock()

	if k.client != nil {
		k.client.Close()
		k.client = nil
	}
	return nil
}

// Produce 发送单条消息，自动注入追踪 headers
func (k *Kafka) Produce(ctx context.Context, topic string, key, value []byte, customHeaders ...map[string]string) error {
	if k == nil {
		return fmt.Errorf("kafka client is nil")
	}
	// 获取或创建追踪上下文
	tc, newCtx := trace.GetOrCreateTraceContext(ctx, trace.SourceMQ, trace.ModeProduction)

	// 设置调用信息
	tc.SetCallInfo("kafka-client", "Produce", "kafka-broker", topic)

	// 生成新的 span
	childTC := tc.NewSpanForCall("kafka-broker", topic)

	// 转换为 headers
	headers := childTC.ToHeaders()

	// 合并自定义 headers
	for _, customHeader := range customHeaders {
		for k, v := range customHeader {
			headers[k] = v
		}
	}

	// 转换为 kgo.RecordHeader
	var kgoHeaders []kgo.RecordHeader
	for k, v := range headers {
		kgoHeaders = append(kgoHeaders, kgo.RecordHeader{
			Key:   k,
			Value: []byte(v),
		})
	}

	// 创建记录
	record := &kgo.Record{
		Topic:   topic,
		Key:     key,
		Value:   value,
		Headers: kgoHeaders,
	}

	// 发送消息
	results := k.client.ProduceSync(newCtx, record)
	if err := results.FirstErr(); err != nil {
		return fmt.Errorf("failed to produce message: %w", err)
	}

	return nil
}

// ProduceMultiple 批量发送消息到多个主题
func (k *Kafka) ProduceMultiple(ctx context.Context, messages []ProduceMessage) error {
	if k == nil {
		return fmt.Errorf("kafka client is nil")
	}
	// 获取或创建追踪上下文
	tc, newCtx := trace.GetOrCreateTraceContext(ctx, trace.SourceMQ, trace.ModeProduction)

	var records []*kgo.Record

	for _, msg := range messages {
		// 为每条消息创建子 span
		childTC := tc.NewSpanForCall("kafka-broker", msg.Topic)
		childTC.SetCallInfo("kafka-client", "ProduceMultiple", "kafka-broker", msg.Topic)

		// 转换为 headers
		headers := childTC.ToHeaders()

		// 合并自定义 headers
		for k, v := range msg.Headers {
			headers[k] = v
		}

		// 转换为 kgo.RecordHeader
		var kgoHeaders []kgo.RecordHeader
		for k, v := range headers {
			kgoHeaders = append(kgoHeaders, kgo.RecordHeader{
				Key:   k,
				Value: []byte(v),
			})
		}

		// 创建记录
		record := &kgo.Record{
			Topic:   msg.Topic,
			Key:     msg.Key,
			Value:   msg.Value,
			Headers: kgoHeaders,
		}

		records = append(records, record)
	}

	// 批量发送消息
	results := k.client.ProduceSync(newCtx, records...)
	if err := results.FirstErr(); err != nil {
		return fmt.Errorf("failed to produce multiple messages: %w", err)
	}

	return nil
}

// ProduceMessage 批量生产消息结构
type ProduceMessage struct {
	Topic   string            `json:"topic"`
	Key     []byte            `json:"key"`
	Value   []byte            `json:"value"`
	Headers map[string]string `json:"headers"`
}

// ConsumeWithHandler 使用处理函数消费消息，自动解析追踪 headers
func (k *Kafka) ConsumeWithHandler(ctx context.Context, topic, groupID string, handler MessageHandler) error {
	if k == nil {
		return fmt.Errorf("kafka client is nil")
	}
	return k.ConsumeMultipleTopics(ctx, []string{topic}, groupID, handler)
}

// ConsumeMultipleTopics 消费多个主题，自动解析追踪 headers
func (k *Kafka) ConsumeMultipleTopics(ctx context.Context, topics []string, groupID string, handler MessageHandler) error {
	if k == nil {
		return fmt.Errorf("kafka client is nil")
	}
	logger := logx.WithContext(ctx)

	// 根据是否包含通配符，决定是否使用正则订阅
	useRegex := false
	for _, t := range topics {
		if strings.Contains(t, "*") {
			useRegex = true
			break
		}
	}

	var fixedTopics []string
	var topicPatterns []string // 仅在 useRegex=true 时使用，包含已锚定的正则
	if useRegex {
		for _, t := range topics {
			if strings.Contains(t, "*") {
				// 将简单通配符转换为严格正则：其它字符转义，* 转为 .*
				pattern := regexp.QuoteMeta(t)
				pattern = strings.ReplaceAll(pattern, "\\*", ".*")
				pattern = "^" + pattern + "$"
				topicPatterns = append(topicPatterns, pattern)
			} else {
				// 固定主题在正则模式下需要严格匹配
				pattern := "^" + regexp.QuoteMeta(t) + "$"
				topicPatterns = append(topicPatterns, pattern)
			}
		}
	} else {
		fixedTopics = append(fixedTopics, topics...)
	}

	// 创建消费者客户端
	opts := []kgo.Opt{
		kgo.SeedBrokers(k.options.Brokers...),
		kgo.ConsumerGroup(groupID),
		kgo.ConsumeResetOffset(kgo.NewOffset().AtStart()), // 从最早开始消费
		kgo.SessionTimeout(k.options.ConsumerSessionTimeout),
		kgo.HeartbeatInterval(k.options.ConsumerHeartbeatInterval),
		kgo.AutoCommitInterval(k.options.ConsumerCommitInterval),
	}

	if useRegex {
		opts = append(opts, kgo.ConsumeRegex())
		opts = append(opts, kgo.ConsumeTopics(topicPatterns...))
	} else if len(fixedTopics) > 0 {
		opts = append(opts, kgo.ConsumeTopics(fixedTopics...))
	}

	consumer, err := kgo.NewClient(opts...)
	if err != nil {
		return fmt.Errorf("failed to create consumer: %w", err)
	}
	defer consumer.Close()

	logger.WithFields(map[string]interface{}{
		"topics":         fixedTopics,
		"regex":          useRegex,
		"topic_patterns": topicPatterns,
		"group":          groupID,
		"brokers":        k.options.Brokers,
	}).Info("starting franz-go multi-topic consumer")

	for {
		select {
		case <-ctx.Done():
			logger.Info("consumer context cancelled, stopping")
			return ctx.Err()
		default:
			// 拉取消息
			fetches := consumer.PollFetches(ctx)
			if errs := fetches.Errors(); len(errs) > 0 {
				for _, err := range errs {
					logger.WithField("error", err.Err.Error()).Error("fetch error")
				}
				continue
			}

			// 处理消息
			fetches.EachPartition(func(p kgo.FetchTopicPartition) {
				for _, record := range p.Records {
					if err := k.handleMessage(ctx, record, handler); err != nil {
						logger.WithFields(map[string]interface{}{
							"error":     err.Error(),
							"topic":     record.Topic,
							"partition": int(record.Partition),
							"offset":    record.Offset,
						}).Error("failed to handle message")
					}
				}
			})

			// 提交偏移量
			if err := consumer.CommitUncommittedOffsets(ctx); err != nil {
				logger.WithError(err).Error("failed to commit offsets")
			}
		}
	}
}

// handleMessage 处理单条消息，自动解析追踪 headers
func (k *Kafka) handleMessage(ctx context.Context, record *kgo.Record, handler MessageHandler) error {
	logger := logx.WithContext(ctx)

	// 解析 headers
	headers := make(map[string]string)
	for _, h := range record.Headers {
		headers[h.Key] = string(h.Value)
	}

	// 从 headers 恢复追踪上下文
	var tc *trace.TraceContext
	var msgCtx context.Context

	if len(headers) > 0 && trace.IsTraceHeader(headers[trace.HeaderTraceID]) {
		// 从 headers 恢复追踪上下文
		tc = trace.FromHeaders(headers)
		msgCtx = trace.WithContext(ctx, tc)
	} else {
		// 创建新的追踪上下文
		tc, msgCtx = trace.GetOrCreateTraceContext(ctx, trace.SourceMQ, trace.ModeProduction)
	}

	// 创建消息处理的子 span
	childTC := tc.NewSpanForCall("message-handler", record.Topic)
	childTC.SetCallInfo("kafka-consumer", "handleMessage", "message-handler", record.Topic)
	msgCtx = trace.WithContext(msgCtx, childTC)

	// 添加超时控制
	timeoutCtx, cancel := context.WithTimeout(msgCtx, k.options.MessageTimeout)
	defer cancel()

	// 添加 trace_id 到 context
	timeoutCtx = ctxx.WithTraceID(timeoutCtx)

	// 创建统一的消息结构
	message := Message{
		Key:       record.Key,
		Value:     record.Value,
		Headers:   headers,
		Topic:     record.Topic,
		Partition: int(record.Partition),
		Offset:    record.Offset,
		Timestamp: record.Timestamp,
	}

	// 记录消息接收日志
	msgLogger := logger.WithFields(map[string]interface{}{
		"topic":     record.Topic,
		"partition": int(record.Partition),
		"offset":    record.Offset,
		"key":       string(record.Key),
		"value":     string(record.Value),
	})

	msgLogger.Info("received message")

	// 调用处理函数
	if err := handler(timeoutCtx, message); err != nil {
		msgLogger.WithError(err).Error("message handler failed")

		// TODO: 实现死信队列逻辑
		if k.options.EnableDeadLetterQueue {
			// 发送到死信队列的逻辑将在这里实现
			msgLogger.Warn("TODO: send message to dead letter queue")
		}

		return err
	}

	return nil
}

// ConsumeSubscriptions 消费多个订阅，自动解析追踪 headers
func (k *Kafka) ConsumeSubscriptions(ctx context.Context, subscriptions []config.KafkaSubscription, handler SubscriptionHandler) error {
	logger := logx.WithContext(ctx)

	enabledSubs := make([]config.KafkaSubscription, 0)
	for _, sub := range subscriptions {
		if sub.Enabled {
			enabledSubs = append(enabledSubs, sub)
		} else {
			logger.WithFields(map[string]interface{}{
				"subscriptions_count": len(subscriptions),
				"subscription":        sub.Name,
			}).Info("subscription disabled, skipping")
		}
	}

	if len(enabledSubs) == 0 {
		return fmt.Errorf("no enabled subscriptions found")
	}

	logger.WithFields(map[string]interface{}{
		"subscriptions":         len(subscriptions),
		"enabled_subscriptions": len(enabledSubs),
	}).Info("starting multi-subscription kafka consumer")

	// 为每个启用的订阅启动独立的消费者
	errChan := make(chan error, len(enabledSubs))

	for _, sub := range enabledSubs {
		go func(subscription config.KafkaSubscription) {
			subLogger := logger.WithFields(map[string]interface{}{
				"subscription": subscription.Name,
				"group_id":     subscription.GroupID,
				"topics":       subscription.Topics,
			})

			subLogger.Info("starting subscription consumer")

			// 为订阅创建专用的处理函数
			subHandler := func(ctx context.Context, message Message) error {
				return handler(ctx, subscription, message)
			}

			// 启动消费者
			if err := k.ConsumeMultipleTopics(ctx, subscription.Topics, subscription.GroupID, subHandler); err != nil {
				subLogger.WithError(err).Error("subscription consumer failed")
				errChan <- fmt.Errorf("subscription %s failed: %w", subscription.Name, err)
			}
		}(sub)
	}

	// 等待任一消费者出错或上下文取消
	select {
	case <-ctx.Done():
		return ctx.Err()
	case err := <-errChan:
		return err
	}
}
