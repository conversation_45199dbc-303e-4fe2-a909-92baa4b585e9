package client

import (
	"context"
	"fmt"
	"midas/internal/config"
	"strings"
	"sync"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/redis/go-redis/v9"
	"github.com/segmentio/kafka-go"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

var (
	once        sync.Once
	dbClient    *gorm.DB
	rdsClient   *redis.Client
	httpClient  *resty.Client
	kafkaWriter *kafka.Writer
	kafkaReader *kafka.Reader
	// cache for per-database connections (app DBs)
	schemaDBs sync.Map // key: dbName string, value: *gorm.DB
)

// InitClients 初始化所有客户端
func InitClients() error {
	var err error
	once.Do(func() {
		err = initAllClients()
	})
	return err
}

// initAllClients 初始化所有客户端
func initAllClients() error {
	cfg := config.Get()
	if cfg == nil {
		return fmt.Errorf("config not loaded")
	}

	// 初始化HTTP客户端
	if err := initHTTPClient(cfg); err != nil {
		return fmt.Errorf("failed to init HTTP client: %w", err)
	}

	// 如果配置了数据库连接信息，则初始化数据库客户端
	if cfg.MySQL.DSN != "" {
		if err := initMySQLClient(cfg); err != nil {
			return fmt.Errorf("failed to init MySQL client: %w", err)
		}
	}

	// 如果配置了Redis连接信息，则初始化Redis客户端
	if cfg.Redis.Addr != "" {
		if err := initRedisClient(cfg); err != nil {
			return fmt.Errorf("failed to init Redis client: %w", err)
		}
	}

	// 初始化 Kafka（在配置存在时）
	if len(cfg.Kafka.Brokers) > 0 {
		if err := initKafkaClients(cfg); err != nil {
			return fmt.Errorf("failed to init Kafka clients: %w", err)
		}
		// 初始化封装后的全局 Kafka 客户端
		kafkaClient = NewKafka(cfg)
	}

	return nil
}

// initHTTPClient 初始化HTTP客户端
func initHTTPClient(cfg *config.Config) error {
	client := resty.New()

	// 设置超时时间
	if cfg.HTTP.Timeout != "" {
		timeout, err := time.ParseDuration(cfg.HTTP.Timeout)
		if err != nil {
			return fmt.Errorf("invalid HTTP timeout: %w", err)
		}
		client.SetTimeout(timeout)
	}

	// 设置重试配置
	if cfg.HTTP.RetryCount > 0 {
		retryWait := 1 * time.Second
		if cfg.HTTP.RetryWait != "" {
			if wait, err := time.ParseDuration(cfg.HTTP.RetryWait); err == nil {
				retryWait = wait
			}
		}

		client.SetRetryCount(cfg.HTTP.RetryCount).
			SetRetryWaitTime(retryWait)
	}

	httpClient = client
	return nil
}

// initMySQLClient 初始化MySQL客户端
func initMySQLClient(cfg *config.Config) error {
	db, err := gorm.Open(mysql.Open(cfg.MySQL.DSN), &gorm.Config{})
	if err != nil {
		return fmt.Errorf("failed to connect to MySQL: %w", err)
	}

	// 获取底层sql.DB以配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("failed to get sql.DB: %w", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(cfg.MySQL.MaxIdle)
	sqlDB.SetMaxOpenConns(cfg.MySQL.MaxOpen)

	if cfg.MySQL.ConnMaxLifetime != "" {
		lifetime, err := time.ParseDuration(cfg.MySQL.ConnMaxLifetime)
		if err != nil {
			return fmt.Errorf("invalid MySQL connection max lifetime: %w", err)
		}
		sqlDB.SetConnMaxLifetime(lifetime)
	}

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := sqlDB.PingContext(ctx); err != nil {
		return fmt.Errorf("failed to ping MySQL: %w", err)
	}

	dbClient = db
	return nil
}

// initRedisClient 初始化Redis客户端
func initRedisClient(cfg *config.Config) error {
	rdb := redis.NewClient(&redis.Options{
		Addr:     cfg.Redis.Addr,
		Password: cfg.Redis.Password,
		Username: cfg.Redis.Username,
		DB:       cfg.Redis.DB,
		PoolSize: cfg.Redis.PoolSize,
	})

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if _, err := rdb.Ping(ctx).Result(); err != nil {
		return fmt.Errorf("failed to ping Redis: %w", err)
	}

	rdsClient = rdb
	return nil
}

// initKafkaClients 初始化 Kafka 生产者和消费者（可选）
func initKafkaClients(cfg *config.Config) error {
	if len(cfg.Kafka.Brokers) == 0 {
		return nil
	}

	// 初始化新的 Kafka 客户端
	initKafka(cfg)

	return nil
}

// GetDB 获取数据库客户端
func GetDB() *gorm.DB {
	return dbClient
}

// GetRedis 获取Redis客户端
func GetRedis() *redis.Client {
	return rdsClient
}

// GetHTTP 获取HTTP客户端
func GetHTTP() *resty.Client {
	return httpClient
}

// Close 关闭所有客户端连接
func Close() error {
	var errs []error

	if dbClient != nil {
		if sqlDB, err := dbClient.DB(); err == nil {
			if err := sqlDB.Close(); err != nil {
				errs = append(errs, fmt.Errorf("failed to close MySQL: %w", err))
			}
		}
	}

	if rdsClient != nil {
		if err := rdsClient.Close(); err != nil {
			errs = append(errs, fmt.Errorf("failed to close Redis: %w", err))
		}
	}

	if kafkaReader != nil {
		if err := kafkaReader.Close(); err != nil {
			errs = append(errs, fmt.Errorf("failed to close Kafka reader: %w", err))
		}
	}
	if kafkaWriter != nil {
		if err := kafkaWriter.Close(); err != nil {
			errs = append(errs, fmt.Errorf("failed to close Kafka writer: %w", err))
		}
	}
	if kafkaClient != nil {
		if err := kafkaClient.Close(); err != nil {
			errs = append(errs, fmt.Errorf("failed to close Kafka client: %w", err))
		}
	}

	if len(errs) > 0 {
		return fmt.Errorf("errors closing clients: %v", errs)
	}

	return nil
}

// GetKafkaWriter 获取 Kafka 生产者
func GetKafkaWriter() *kafka.Writer {
	return kafkaWriter
}

// GetKafkaReader 获取 Kafka 消费者
func GetKafkaReader() *kafka.Reader {
	return kafkaReader
}

// GetDBForDatabase returns a gorm DB connected to the given database name,
// based on the base DSN in config. Connections are cached per dbName.
func GetDBForDatabase(ctx context.Context, dbName string) (*gorm.DB, error) {
	if dbName == "" {
		return nil, fmt.Errorf("dbName is required")
	}

	if v, ok := schemaDBs.Load(dbName); ok {
		if existing, ok2 := v.(*gorm.DB); ok2 {
			return existing.WithContext(ctx), nil
		}
	}

	cfg := config.Get()
	if cfg == nil {
		return nil, fmt.Errorf("config not loaded")
	}

	dsn, err := buildDSNForDatabase(cfg.MySQL.DSN, dbName)
	if err != nil {
		return nil, err
	}

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to MySQL for database %s: %w", dbName, err)
	}
	// pool settings mirror the base one
	sqlDB, err := db.DB()
	if err == nil {
		sqlDB.SetMaxIdleConns(cfg.MySQL.MaxIdle)
		sqlDB.SetMaxOpenConns(cfg.MySQL.MaxOpen)
		if cfg.MySQL.ConnMaxLifetime != "" {
			if lifetime, e := time.ParseDuration(cfg.MySQL.ConnMaxLifetime); e == nil {
				sqlDB.SetConnMaxLifetime(lifetime)
			}
		}
		// ping with timeout
		pingCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
		defer cancel()
		if e := sqlDB.PingContext(pingCtx); e != nil {
			return nil, fmt.Errorf("failed to ping MySQL %s: %w", dbName, e)
		}
	}

	schemaDBs.Store(dbName, db)
	return db.WithContext(ctx), nil
}

// buildDSNForDatabase replaces the database name segment in a DSN.
// It assumes the DSN has the form: user:pass@net(addr)/dbname?params
func buildDSNForDatabase(baseDSN, databaseName string) (string, error) {
	if baseDSN == "" {
		return "", fmt.Errorf("base DSN is empty")
	}
	qIdx := strings.Index(baseDSN, "?")
	if qIdx == -1 {
		qIdx = len(baseDSN)
	}
	slashIdx := strings.LastIndex(baseDSN[:qIdx], "/")
	if slashIdx == -1 {
		return "", fmt.Errorf("invalid DSN format: missing '/' before db name")
	}
	prefix := baseDSN[:slashIdx+1]
	suffix := baseDSN[qIdx:]
	return prefix + databaseName + suffix, nil
}
