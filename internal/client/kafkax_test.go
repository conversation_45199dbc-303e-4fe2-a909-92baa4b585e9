package client

import (
	"context"
	"midas/internal/config"
	"midas/internal/pkg/trace"
	"testing"
	"time"

	"github.com/segmentio/kafka-go"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestDefaultKafkaOptions 测试默认配置
func TestDefaultKafkaOptions(t *testing.T) {
	options := DefaultKafkaOptions()

	assert.Equal(t, 100, options.ProducerBatchSize)
	assert.Equal(t, 10*time.Millisecond, options.ProducerBatchTimeout)
	assert.Equal(t, 1, options.ProducerRequiredAcks)
	assert.Equal(t, 3, options.ProducerRetryMax)
	assert.False(t, options.ProducerAsync)

	assert.Equal(t, 1, options.ConsumerMinBytes)
	assert.Equal(t, 10*1024*1024, options.ConsumerMaxBytes)
	assert.Equal(t, 1*time.Second, options.ConsumerMaxWait)
	assert.Equal(t, 1*time.Second, options.ConsumerCommitInterval)
	assert.Equal(t, kafka.FirstOffset, options.ConsumerStartOffset)
	assert.Equal(t, 30*time.Second, options.ConsumerSessionTimeout)
	assert.Equal(t, 3*time.Second, options.ConsumerHeartbeatInterval)

	assert.Equal(t, 10*time.Second, options.DialTimeout)
	assert.Equal(t, 10*time.Second, options.ReadTimeout)
	assert.Equal(t, 10*time.Second, options.WriteTimeout)

	assert.False(t, options.EnableDeadLetterQueue)
	assert.Equal(t, 3, options.MaxRetries)
	assert.Equal(t, 1*time.Second, options.RetryBackoff)

	assert.False(t, options.EnableMetrics)
	assert.False(t, options.EnableAlerts)

	assert.Equal(t, 30*time.Second, options.MessageTimeout)
	assert.Equal(t, 1, options.MaxConcurrency)
}

// TestNewKafka 测试 Kafka 客户端创建
func TestNewKafka(t *testing.T) {
	cfg := &config.Config{
		Kafka: config.KafkaConfig{
			Brokers: []string{"localhost:9092"},
			Topic:   "test-topic",
			GroupID: "test-group",
		},
	}

	// 测试默认配置
	kafka := NewKafka(cfg)
	require.NotNil(t, kafka)
	assert.Equal(t, cfg, kafka.cfg)
	assert.NotNil(t, kafka.options)
	assert.Equal(t, []string{"localhost:9092"}, kafka.options.Brokers)

	// 测试自定义配置
	customOptions := &KafkaOptions{
		Brokers:               []string{"localhost:9093"},
		ProducerBatchSize:     200,
		MessageTimeout:        60 * time.Second,
		MaxConcurrency:        4,
		EnableDeadLetterQueue: true,
		DeadLetterTopic:       "test-dlq",
	}

	kafka2 := NewKafka(cfg, customOptions)
	require.NotNil(t, kafka2)
	assert.Equal(t, customOptions, kafka2.options)
	assert.Equal(t, []string{"localhost:9093"}, kafka2.options.Brokers)
	assert.Equal(t, 200, kafka2.options.ProducerBatchSize)
	assert.Equal(t, 60*time.Second, kafka2.options.MessageTimeout)
	assert.Equal(t, 4, kafka2.options.MaxConcurrency)
	assert.True(t, kafka2.options.EnableDeadLetterQueue)
	assert.Equal(t, "test-dlq", kafka2.options.DeadLetterTopic)
}

// TestNewKafkaWithNilConfig 测试空配置
func TestNewKafkaWithNilConfig(t *testing.T) {
	cfg := &config.Config{
		Kafka: config.KafkaConfig{
			Brokers: []string{"localhost:9092"},
		},
	}
	kafka := NewKafka(cfg)
	// 应该创建客户端（使用默认配置）
	assert.NotNil(t, kafka)
	assert.NotNil(t, kafka.options)
}

// TestNewKafkaWithEmptyBrokers 测试空 brokers 配置
func TestNewKafkaWithEmptyBrokers(t *testing.T) {
	cfg := &config.Config{
		Kafka: config.KafkaConfig{
			Brokers: []string{"localhost:9092"},
			Topic:   "test-topic",
			GroupID: "test-group",
		},
	}

	// 自定义配置中没有指定 brokers，应该使用配置文件中的
	customOptions := &KafkaOptions{
		ProducerBatchSize: 200,
	}

	kafka := NewKafka(cfg, customOptions)
	require.NotNil(t, kafka)
	assert.Equal(t, []string{"localhost:9092"}, kafka.options.Brokers)
}

// TestGenerateTraceID 测试 trace ID 生成
func TestGenerateTraceID(t *testing.T) {
	traceID1 := trace.GenerateTraceID(trace.SourceService)
	traceID2 := trace.GenerateTraceID(trace.SourceService)

	assert.NotEmpty(t, traceID1)
	assert.NotEmpty(t, traceID2)
	assert.NotEqual(t, traceID1, traceID2) // 应该生成不同的 ID
	assert.Equal(t, 16, len(traceID1))     // 8 字节的十六进制表示应该是 16 个字符
}

// TestKafkaClose 测试客户端关闭
func TestKafkaClose(t *testing.T) {
	cfg := &config.Config{
		Kafka: config.KafkaConfig{
			Brokers: []string{"localhost:9092"},
			Topic:   "test-topic",
			GroupID: "test-group",
		},
	}

	kafka := NewKafka(cfg)
	require.NotNil(t, kafka)

	// 关闭应该成功（即使没有创建任何连接）
	err := kafka.Close()
	assert.NoError(t, err)
}

// TestProduceValidation 测试生产消息的参数验证
func TestProduceValidation(t *testing.T) {
	cfg := &config.Config{
		Kafka: config.KafkaConfig{
			Brokers: []string{"localhost:9092"},
			Topic:   "test-topic",
			GroupID: "test-group",
		},
	}

	kafka := NewKafka(cfg)
	require.NotNil(t, kafka)

	ctx := context.Background()

	// 测试空客户端
	var nilKafka *Kafka
	err := nilKafka.Produce(ctx, "topic", []byte("key"), []byte("value"), nil)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "kafka client is nil")

	// 测试空主题（应该使用配置中的默认主题）
	// 注意：这个测试不会实际连接到 Kafka，只是测试参数验证
	// 实际的网络连接测试需要在集成测试中进行
}

// TestConsumeWithHandlerValidation 测试消费消息的参数验证
func TestConsumeWithHandlerValidation(t *testing.T) {
	cfg := &config.Config{
		Kafka: config.KafkaConfig{
			Brokers: []string{"localhost:9092"},
			Topic:   "test-topic",
			GroupID: "test-group",
		},
	}

	kafka := NewKafka(cfg)
	require.NotNil(t, kafka)

	ctx := context.Background()

	// 测试空客户端
	var nilKafka *Kafka
	err := nilKafka.ConsumeWithHandler(ctx, "test-topic", "test-group", nil)
	assert.Error(t, err)

	// 测试空处理函数
	err = kafka.ConsumeWithHandler(ctx, "test-topic", "test-group", nil)
	assert.Error(t, err)
}

// TestProduceWithHeaders 测试带 headers 的消息生产
func TestProduceWithHeaders(t *testing.T) {
	cfg := &config.Config{
		Kafka: config.KafkaConfig{
			Brokers: []string{"localhost:9092"},
			Topic:   "test-topic",
			GroupID: "test-group",
		},
	}

	kafka := NewKafka(cfg)
	require.NotNil(t, kafka)

	ctx := context.Background()

	// 测试正常生产（不会真正发送，因为没有真实的 Kafka）
	err := kafka.Produce(ctx, "test-topic", []byte("key"), []byte("value"), map[string]string{"test": "header"})
	// 这里会失败，因为没有真实的 Kafka 连接，但这是预期的
	assert.Error(t, err)
}

// BenchmarkGenerateTraceID 性能测试：trace ID 生成
func BenchmarkGenerateTraceID(b *testing.B) {
	for i := 0; i < b.N; i++ {
		trace.GenerateTraceID(trace.SourceService)
	}
}

// BenchmarkNewKafka 性能测试：Kafka 客户端创建
func BenchmarkNewKafka(b *testing.B) {
	cfg := &config.Config{
		Kafka: config.KafkaConfig{
			Brokers: []string{"localhost:9092"},
			Topic:   "test-topic",
			GroupID: "test-group",
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		kafka := NewKafka(cfg)
		kafka.Close()
	}
}
