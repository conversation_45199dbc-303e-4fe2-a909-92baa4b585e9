# Midas 项目 Prompt（给自动化助手）

## 1. 背景速览
- 类型：Go 1.22+ CLI，用于苹果退款场景的离线处理。
- 核心命令：`sync-refund`（CDC 消费）、`process-refund-requests`、Kafka 调试工具。
- 架构：`cmd/cli` + `internal/cli`（Cobra 命令），`internal/client`（MySQL/Redis/HTTP/Kafka 封装），`internal/pkg/refund`（CDC 处理器），`internal/models`（GORM 模型）。
- 配置：TOML (`configs/app.toml`)；Kafka 启用时需同时提供 legacy `topic` 字段以兼容旧命令。

## 2. 开发约定
- **错误处理**：所有外部调用必须检查 `err`，必要时使用 `fmt.Errorf` 包装上下文。
- **Context 透传**：跨层函数的第一个参数为 `context.Context`，结合 `ctxx` 获取 `run_id` / `trace_id`。
- **日志**：使用 `logx.WithContext(ctx)` 输出结构化日志；生产环境按小时滚动，保留 7 天。
- **时间与时区**：数据库写入使用 `time.Now().UTC()`，保持与旧系统一致。
- **数据库访问**：统一通过 GORM；`client.GetDBForDatabase` 会基于主 DSN 重连各业务库。
- **Kafka**：默认使用 franz-go；消费回调必须幂等，避免重复消息导致脏数据。若需修改批量/重试策略，参考 `KafkaOptions`。

## 3. 代码结构要点
```text
cmd/cli/main.go        # 入口：加载配置、捕获 SIGTERM
internal/cli/*         # 各子命令实现，关注 runE
internal/client/*      # InitClients、HTTP/Redis/MySQL/Kafka 封装
internal/pkg/refund    # Debezium 事件处理 (SyncHandler)
internal/models        # GORM 模型与领域函数
```

- `sync-refund` 只能消费 `apple_notifications_cdc` 订阅；新增订阅需在 `filterRefundSubscriptions` 扩展。
- `process-refund-requests` 目前使用 `callAppleASCStub` 占位；接入真实 ASC API 时需补全重试、失败记录。
- `models.ComputeRefundInfo` 决定回传字段，修改策略时需同时校验 `refund_policy` 数据。

## 4. 测试与调试
- 单元测试覆盖较少，可通过嵌入式测试或 Dry-Run (`sync-refund debug`) 验证逻辑。
- 依赖 Kafka 的改动建议配合 `docker/README.md` 搭建本地环境。
- 调试 SQL 时，请使用 `ctx` 带来的 `run_id` 方便关联日志。

## 5. 文档维护
- 项目简介仅放在 `README.md`；其他说明整理至 `doc/` 或 `docker/`（已按主题拆分）。
- 新增文档时在 README 的“文档导航”补充条目，保持目录清晰。
- 若修改容器编排，请同步更新 `docker/README.md` 与 `connector-config.json` 示例。

## 6. 常见任务指引
- **新增 CDC 字段**：修改 `internal/pkg/refund/sync_handler.go` 中的解析函数，并在 `models` 定义字段；同时更新 `doc/refund-sync.md`。
- **接入新策略**：更新 `models.RefundPolicy` 相关逻辑，补充 `ComputeRefundInfo`，并在文档中说明策略 ID 与调度规则。
- **接入监控**：可在 Kafka 消费回调中增加指标采集，或利用 `trace` 包扩展 `X-Compute-*` 字段。

遵循以上约定可以最大化与现有代码保持一致，避免引入与旧系统不兼容的行为。
