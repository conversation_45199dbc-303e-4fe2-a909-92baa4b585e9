#!/usr/bin/env python3
"""Scan MySQL schemas and generate CDC volume reports."""

from __future__ import annotations

import argparse
import datetime as dt
import html
import json
import math
import sys
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List, Optional, Sequence, Tuple

try:
    import pymysql
    from pymysql import err as mysql_errors
    from pymysql.cursors import DictCursor
except ModuleNotFoundError as exc:  # pragma: no cover - import guard only
    print(
        "Missing dependency: pymysql. Install with `pip install pymysql` and retry.",
        file=sys.stderr,
    )
    raise


DEFAULT_DSN = "app_read:9dzp9aPveFTKUADC@tcp(rm-t4n2fbcs0kn2142myjo.mysql.singapore.rds.aliyuncs.com:3306)"
TARGET_TABLE = "apple_server_notifications"
ESTIMATED_DEBEZIUM_OVERHEAD_BYTES = 320
MINIMUM_ROW_PAYLOAD_BYTES = 128
CREAT_COLUMN_PREFIX = "creat"
CREAT_COLUMN_PRIORITIES = [
    "created_at",
    "creation_date",
    "create_time",
    "creation_time",
    "created_time",
    "created",
]
SUPPORTED_TIME_TYPES = {"timestamp", "datetime"}
SKIP_TABLES = {"track_receipt_tasks", "account_receipt_data"}
MAX_EXECUTION_TIME_MS = 45000
DEFAULT_TABLE_COLUMNS = [
    "account_transactions",
    "accounts",
    "apple_push_logs",
    "apple_server_notifications",
    "devices",
    "experiment_group_map_users",
    "experiment_groups",
    "experiment_white_users",
    "experiments",
    "iad_attributions",
    "remote_action_map_users",
    "remote_actions",
    "target_config_rule_match_users",
    "target_config_rules",
    "targeting_configs",
    "traffic_layers",
    "visit_logs",
]


class SkipRecorder:
    def __init__(self, path: Path) -> None:
        self.path = path
        self.records: List[Tuple[str, str, str]] = []

    def add(self, schema: str, table: str, reason: str) -> None:
        self.records.append((schema, table, reason))

    def flush(self) -> None:
        existing_rows: set[str] = set()
        if self.path.exists():
            for line in self.path.read_text(encoding="utf-8").strip().splitlines():
                if line.startswith("| ") and " |" in line and "原因" not in line:
                    existing_rows.add(line)
        for schema, table, reason in self.records:
            existing_rows.add(f"| {schema} | {table} | {reason} |")

        lines = ["# Skipped Tables", "", "| 库名 | 表名 | 原因 |", "| --- | --- | --- |"]
        lines.extend(sorted(existing_rows))
        self.path.write_text("\n".join(lines) + "\n", encoding="utf-8")


class ProgressLogger:
    def __init__(self, total: int, log_path: Path, skip_recorder: Optional[SkipRecorder] = None) -> None:
        self.total = total
        self.current = 0
        self._file = log_path.open("w", encoding="utf-8")
        self._write(f"总计计划执行表数: {total}")
        self.skip_recorder = skip_recorder

    def _write(self, line: str) -> None:
        self._file.write(line + "\n")
        self._file.flush()

    def log_skip(self, schema: str, table: str, reason: str, count: bool = False) -> None:
        if count:
            self.current += 1
            prefix = f"[SKIP {self.current}/{self.total}]"
        else:
            prefix = "[SKIP]"
        line = f"{prefix} {schema}.{table} - {reason}"
        print(line, flush=True)
        self._write(line)
        if self.skip_recorder and "超时" in reason:
            self.skip_recorder.add(schema, table, reason)

    def log_step(self, schema: str, table: str, status: str) -> None:
        self.current += 1
        denominator = self.total if self.total else 1
        line = f"[{self.current}/{denominator}] {schema}.{table} - {status}"
        print(line, flush=True)
        self._write(line)

    def close(self) -> None:
        try:
            self._file.close()
        except Exception:
            pass


@dataclass
class TableWindowStats:
    schema: str
    table: str
    timestamp_column: Optional[str]
    row_count: Optional[int]
    avg_row_length: Optional[float]
    data_length: Optional[float]
    index_length: Optional[float]
    storage_bytes_estimate: Optional[float]
    debezium_min_bytes: Optional[float]
    debezium_max_bytes: Optional[float]
    count_query: Optional[str]
    error: Optional[str]

    def human_count(self) -> str:
        return "-" if self.row_count is None else str(self.row_count)

    def human_debezium_range(self) -> str:
        if self.debezium_min_bytes is None or self.debezium_max_bytes is None:
            return "-"
        return (
            f"{human_readable_bytes(self.debezium_min_bytes)}~"
            f"{human_readable_bytes(self.debezium_max_bytes)}"
        )


@dataclass
class AggregatedStats:
    schema: str
    tables: List[TableWindowStats]

    @property
    def total_rows(self) -> int:
        return sum(stat.row_count or 0 for stat in self.tables)

    @property
    def total_debezium_bytes(self) -> float:
        return sum(stat.debezium_max_bytes or 0.0 for stat in self.tables)


class MySQLScanner:
    def __init__(self, dsn: str, connect_timeout: int = 10) -> None:
        self._dsn = dsn
        self._connect_timeout = connect_timeout
        self._conn = self._connect()
        self._apply_session_settings()

    def _connect(self) -> pymysql.connections.Connection:
        cfg = parse_mysql_dsn(self._dsn)
        read_write_timeout = max(self._connect_timeout * 6, 60)
        return pymysql.connect(
            host=cfg.host,
            port=cfg.port,
            user=cfg.user,
            password=cfg.password,
            charset="utf8mb4",
            cursorclass=DictCursor,
            connect_timeout=self._connect_timeout,
            read_timeout=read_write_timeout,
            write_timeout=read_write_timeout,
        )

    def _apply_session_settings(self) -> None:
        try:
            with self._conn.cursor() as cursor:
                cursor.execute("SET SESSION MAX_EXECUTION_TIME=%s", (MAX_EXECUTION_TIME_MS,))
        except Exception:
            pass

    def _ensure_connection(self) -> None:
        try:
            self._conn.ping(reconnect=True)
        except Exception:
            self._conn = self._connect()
            self._apply_session_settings()

    def close(self) -> None:
        try:
            self._conn.close()
        except Exception:
            pass

    def schemas_with_table(self, table_name: str) -> List[str]:
        self._ensure_connection()
        sql = (
            "SELECT DISTINCT table_schema "
            "FROM information_schema.tables "
            "WHERE table_schema NOT IN ('information_schema', 'mysql', 'performance_schema', 'sys') "
            "AND table_name = %s"
        )
        with self._conn.cursor() as cursor:
            cursor.execute(sql, (table_name,))
            rows = cursor.fetchall()
        return sorted(row["table_schema"] for row in rows)

    def fetch_table_metadata(self, schema: str) -> Dict[str, Dict[str, Optional[float]]]:
        self._ensure_connection()
        sql = (
            "SELECT table_name, table_rows, data_length, index_length, avg_row_length "
            "FROM information_schema.tables WHERE table_schema = %s"
        )
        try:
            with self._conn.cursor() as cursor:
                cursor.execute(sql, (schema,))
                return {
                    row["table_name"]: {
                        "table_rows": row["table_rows"],
                        "data_length": row["data_length"],
                        "index_length": row["index_length"],
                        "avg_row_length": row["avg_row_length"],
                    }
                    for row in cursor.fetchall()
                }
        except mysql_errors.MySQLError as exc:
            print(
                f"[WARN] Failed to load table metadata for {schema}: {exc}",
                file=sys.stderr,
            )
            self._ensure_connection()
            return {}

    def fetch_time_columns(self, schema: str, table: str) -> List[Tuple[str, str]]:
        self._ensure_connection()
        sql = (
            "SELECT column_name, data_type FROM information_schema.columns "
            "WHERE table_schema = %s AND table_name = %s"
        )
        try:
            with self._conn.cursor() as cursor:
                cursor.execute(sql, (schema, table))
                columns = cursor.fetchall()
        except mysql_errors.MySQLError as exc:
            print(
                f"[WARN] Failed to load columns for {schema}.{table}: {exc}",
                file=sys.stderr,
            )
            self._ensure_connection()
            return []
        return [
            (column["column_name"], column["data_type"].lower())
            for column in columns
        ]

    def fetch_creat_columns(self, schema: str) -> Dict[str, List[Tuple[str, str]]]:
        self._ensure_connection()
        sql = (
            "SELECT table_name, column_name, data_type "
            "FROM information_schema.columns "
            "WHERE table_schema = %s AND LOWER(column_name) LIKE %s"
        )
        try:
            with self._conn.cursor() as cursor:
                cursor.execute(sql, (schema, f"{CREAT_COLUMN_PREFIX}%"))
                rows = cursor.fetchall()
        except mysql_errors.MySQLError as exc:
            print(
                f"[WARN] Failed to load creat* columns for {schema}: {exc}",
                file=sys.stderr,
            )
            self._ensure_connection()
            return {}
        result: Dict[str, List[Tuple[str, str]]] = {}
        for row in rows:
            dtype = (row["data_type"] or "").lower()
            if dtype not in SUPPORTED_TIME_TYPES:
                continue
            table_name = row["table_name"]
            result.setdefault(table_name, []).append((row["column_name"], dtype))
        return result

    def count_rows_since(
        self,
        schema: str,
        table: str,
        timestamp_column: str,
        window_start: dt.datetime,
    ) -> Tuple[Optional[int], Optional[str], bool]:
        self._ensure_connection()
        sql = (
            "SELECT /*+ MAX_EXECUTION_TIME({}) */ COUNT(*) AS row_count "
            "FROM `{}`.`{}` WHERE `{}` >= %s"
        ).format(MAX_EXECUTION_TIME_MS, schema, table, timestamp_column)
        with self._conn.cursor() as cursor:
            try:
                cursor.execute(sql, (window_start,))
                result = cursor.fetchone()
            except Exception as exc:
                print(
                    f"[WARN] Failed to count rows for {schema}.{table} using column {timestamp_column}: {exc}",
                    file=sys.stderr,
                )
                self._ensure_connection()
                message = str(exc)
                code = exc.args[0] if isinstance(exc, mysql_errors.MySQLError) and exc.args else None
                timeout_error = False
                if isinstance(exc, mysql_errors.MySQLError):
                    timeout_error = code in {3024, 2013, 1205, 1206}
                if "max_execution_time" in message.lower() or "timeout" in message.lower():
                    timeout_error = True
                return None, message, timeout_error
        if result and result["row_count"] is not None:
            return int(result["row_count"]), None, False
        return None, "Empty result", False

    def scan_schema(
        self,
        schema: str,
        window_start: dt.datetime,
        planned_tables: Sequence[Tuple[str, str, Dict[str, Optional[float]]]],
        progress: Optional["ProgressLogger"] = None,
    ) -> AggregatedStats:
        tables: List[TableWindowStats] = []
        for table, timestamp_column, stats in planned_tables:
            row_count, error, timed_out = self.count_rows_since(
                schema, table, timestamp_column, window_start
            )
            if timed_out:
                if progress:
                    progress.log_skip(schema, table, "COUNT 超时，已跳过", count=True)
                continue
            avg_row_length = to_float(stats.get("avg_row_length"))
            data_length = to_float(stats.get("data_length"))
            index_length = to_float(stats.get("index_length"))
            table_rows = to_float(stats.get("table_rows"))
            payload_bytes = compute_payload_bytes(
                avg_row_length=avg_row_length,
                data_length=data_length,
                index_length=index_length,
                table_rows=table_rows,
            )
            storage_bytes = estimate_storage_bytes(row_count=row_count, payload_bytes=payload_bytes)
            debezium_min, debezium_max = estimate_debezium_range(
                row_count=row_count,
                payload_bytes=payload_bytes,
            )
            count_query = build_count_query(schema, table, timestamp_column, window_start)
            tables.append(
                TableWindowStats(
                    schema=schema,
                    table=table,
                    timestamp_column=timestamp_column,
                    row_count=row_count,
                    avg_row_length=avg_row_length,
                    data_length=data_length,
                    index_length=index_length,
                    storage_bytes_estimate=storage_bytes,
                    debezium_min_bytes=debezium_min,
                    debezium_max_bytes=debezium_max,
                    count_query=count_query,
                    error=error,
                )
            )
            if progress:
                status_bits = []
                status_bits.append(f"rows={row_count if row_count is not None else 'N/A'}")
                status_bits.append(
                    "storage=" + human_readable_bytes(storage_bytes)
                    if storage_bytes is not None
                    else "storage=-"
                )
                if debezium_min is not None and debezium_max is not None:
                    status_bits.append(
                        "debezium="
                        + human_readable_bytes(debezium_min)
                        + "~"
                        + human_readable_bytes(debezium_max)
                    )
                elif debezium_max is not None:
                    status_bits.append("debezium=" + human_readable_bytes(debezium_max))
                if error:
                    status_bits.append(f"error={error}")
                else:
                    status_bits.append("ok")
                progress.log_step(schema, table, "; ".join(status_bits))
        return AggregatedStats(schema=schema, tables=tables)


@dataclass
class DSNConfig:
    user: str
    password: str
    host: str
    port: int


def parse_mysql_dsn(dsn: str) -> DSNConfig:
    try:
        credentials, location = dsn.split("@", 1)
        user, password = credentials.split(":", 1)
        if not location.startswith("tcp(") or not location.endswith(")"):
            raise ValueError
        host, port_text = location[4:-1].split(":", 1)
        port = int(port_text)
    except Exception as exc:  # pragma: no cover - defensive parsing
        raise ValueError(
            "DSN must look like user:pass@tcp(host:port)"
        ) from exc
    return DSNConfig(user=user, password=password, host=host, port=port)


def choose_creat_timestamp_column(columns: Sequence[Tuple[str, str]]) -> Optional[str]:
    if not columns:
        return None
    candidates = [
        (name, dtype)
        for name, dtype in ((col_name, col_type.lower()) for col_name, col_type in columns)
        if dtype in SUPPORTED_TIME_TYPES and name.lower().startswith(CREAT_COLUMN_PREFIX)
    ]
    if not candidates:
        return None
    lower_map = {name.lower(): name for name, _ in candidates}
    for preference in CREAT_COLUMN_PRIORITIES:
        if preference in lower_map:
            return lower_map[preference]
    return candidates[0][0]


def compute_payload_bytes(
    avg_row_length: Optional[float],
    data_length: Optional[float],
    index_length: Optional[float],
    table_rows: Optional[float],
) -> float:
    payload_bytes: Optional[float] = None
    if avg_row_length and avg_row_length > 0:
        payload_bytes = avg_row_length
    elif table_rows and table_rows > 0:
        total_bytes = (data_length or 0.0) + (index_length or 0.0)
        if total_bytes > 0:
            payload_bytes = total_bytes / table_rows
    if not payload_bytes or payload_bytes <= 0:
        payload_bytes = MINIMUM_ROW_PAYLOAD_BYTES
    return max(payload_bytes, MINIMUM_ROW_PAYLOAD_BYTES)


def estimate_debezium_range(
    row_count: Optional[int],
    payload_bytes: float,
) -> Tuple[Optional[float], Optional[float]]:
    if row_count is None:
        return None, None
    if row_count == 0:
        return 0.0, 0.0
    min_payload = max(MINIMUM_ROW_PAYLOAD_BYTES, payload_bytes * 0.8)
    max_payload = max(min_payload, payload_bytes)
    min_event_size = min_payload + ESTIMATED_DEBEZIUM_OVERHEAD_BYTES
    max_event_size = max_payload + ESTIMATED_DEBEZIUM_OVERHEAD_BYTES
    return row_count * min_event_size, row_count * max_event_size


def estimate_storage_bytes(
    row_count: Optional[int],
    payload_bytes: float,
) -> Optional[float]:
    if row_count is None:
        return None
    if row_count == 0:
        return 0.0
    return row_count * payload_bytes


def build_count_query(
    schema: str,
    table: str,
    timestamp_column: str,
    window_start: dt.datetime,
) -> str:
    boundary = window_start.strftime("%Y-%m-%d %H:%M:%S")
    return (
        f"SELECT COUNT(*) AS row_count FROM `{schema}`.`{table}` "
        f"WHERE `{timestamp_column}` >= '{boundary}'"
    )


def to_float(value: Optional[float]) -> Optional[float]:
    if value is None:
        return None
    if isinstance(value, (int, float)):
        return float(value)
    try:
        return float(value)
    except (TypeError, ValueError):
        return None


def human_readable_bytes(size_bytes: Optional[float]) -> str:
    if size_bytes is None:
        return "-"
    size = float(size_bytes)
    if size <= 0:
        return "0 B"
    units = ["B", "KB", "MB", "GB", "TB", "PB"]
    magnitude = int(math.log(size, 1024))
    magnitude = min(magnitude, len(units) - 1)
    scaled = size / (1024 ** magnitude)
    return f"{scaled:.2f} {units[magnitude]}"


def format_table(stats: TableWindowStats) -> str:
    ts_col = stats.timestamp_column or "-"
    avg_row = f"{stats.avg_row_length:.1f}" if stats.avg_row_length else "-"
    data_len = human_readable_bytes(stats.data_length)
    index_len = human_readable_bytes(stats.index_length)
    storage_len = human_readable_bytes(stats.storage_bytes_estimate)
    debezium_range = stats.human_debezium_range()
    error_text = f" | error={stats.error}" if stats.error else ""
    return (
        f"  {stats.table:<40} | ts_col={ts_col:<20} | rows_48h={stats.human_count():>10} "
        f"| avg_row_bytes={avg_row:>8} | data={data_len:>10} | index={index_len:>10} "
        f"| storage≈{storage_len:>10} | debezium≈{debezium_range:>20}{error_text}"
    )


def build_dataset(
    aggregated: List[AggregatedStats],
    hours: int,
    window_start: dt.datetime,
) -> Dict[str, Any]:
    table_names = sorted({stat.table for agg in aggregated for stat in agg.tables})
    database_order = [agg.schema for agg in aggregated]

    def init_totals() -> Dict[str, Any]:
        return {
            "rows": 0,
            "storageBytes": 0.0,
            "debeziumMinBytes": 0.0,
            "debeziumMaxBytes": 0.0,
        }

    table_totals: Dict[str, Dict[str, Any]] = {
        table: {**init_totals(), "presentIn": []} for table in table_names
    }
    overall_totals = init_totals()
    database_entries: List[Dict[str, Any]] = []

    for agg in aggregated:
        table_map = {stat.table: stat for stat in agg.tables}
        db_totals = init_totals()
        table_cells: Dict[str, Dict[str, Any]] = {}
        for table in table_names:
            stat = table_map.get(table)
            if not stat:
                continue
            cell = {
                "schema": stat.schema,
                "table": stat.table,
                "rows": stat.row_count,
                "storageBytes": stat.storage_bytes_estimate,
                "debeziumMinBytes": stat.debezium_min_bytes,
                "debeziumMaxBytes": stat.debezium_max_bytes,
                "timestampColumn": stat.timestamp_column,
                "query": stat.count_query,
                "error": stat.error,
            }
            table_cells[table] = cell
            table_totals[table]["presentIn"].append(agg.schema)

            if stat.row_count is not None:
                db_totals["rows"] += stat.row_count
                overall_totals["rows"] += stat.row_count
                table_totals[table]["rows"] += stat.row_count
            if stat.storage_bytes_estimate is not None:
                db_totals["storageBytes"] += stat.storage_bytes_estimate
                overall_totals["storageBytes"] += stat.storage_bytes_estimate
                table_totals[table]["storageBytes"] += stat.storage_bytes_estimate
            if stat.debezium_min_bytes is not None:
                db_totals["debeziumMinBytes"] += stat.debezium_min_bytes
                overall_totals["debeziumMinBytes"] += stat.debezium_min_bytes
                table_totals[table]["debeziumMinBytes"] += stat.debezium_min_bytes
            if stat.debezium_max_bytes is not None:
                db_totals["debeziumMaxBytes"] += stat.debezium_max_bytes
                overall_totals["debeziumMaxBytes"] += stat.debezium_max_bytes
                table_totals[table]["debeziumMaxBytes"] += stat.debezium_max_bytes

        database_entries.append(
            {
                "name": agg.schema,
                "totals": db_totals,
                "tables": table_cells,
            }
        )

    table_entries = [
        {
            "name": table,
            "totals": {
                "rows": totals["rows"],
                "storageBytes": totals["storageBytes"],
                "debeziumMinBytes": totals["debeziumMinBytes"],
                "debeziumMaxBytes": totals["debeziumMaxBytes"],
                "presentIn": totals["presentIn"],
            },
        }
        for table, totals in table_totals.items()
    ]

    dataset = {
        "hours": hours,
        "windowStart": window_start.replace(microsecond=0).isoformat(),
        "generatedAt": dt.datetime.utcnow().replace(microsecond=0).isoformat(),
        "databaseOrder": database_order,
        "tableOrder": table_names,
        "databases": database_entries,
        "tables": table_entries,
        "overall": overall_totals,
    }
    return dataset


def accumulate_totals(target: Dict[str, float], cell: Dict[str, Any]) -> None:
    rows = cell.get("rows")
    if isinstance(rows, int):
        target["rows"] += rows
    storage = cell.get("storageBytes")
    if isinstance(storage, (int, float)):
        target["storageBytes"] += storage
    debezium_min = cell.get("debeziumMinBytes")
    if isinstance(debezium_min, (int, float)):
        target["debeziumMinBytes"] += debezium_min
    debezium_max = cell.get("debeziumMaxBytes")
    if isinstance(debezium_max, (int, float)):
        target["debeziumMaxBytes"] += debezium_max


def rebuild_dataset_from_entries(
    database_entries_map: Dict[str, Dict[str, Any]],
    hours: int,
    window_start_iso: str,
) -> Dict[str, Any]:
    database_order = sorted(database_entries_map.keys())
    table_names = sorted(
        {
            table
            for entry in database_entries_map.values()
            for table in entry.get("tables", {}).keys()
        }
    )

    def init_totals() -> Dict[str, float]:
        return {
            "rows": 0,
            "storageBytes": 0.0,
            "debeziumMinBytes": 0.0,
            "debeziumMaxBytes": 0.0,
        }

    table_totals: Dict[str, Dict[str, Any]] = {
        table: {**init_totals(), "presentIn": []} for table in table_names
    }
    overall_totals = init_totals()
    database_entries: List[Dict[str, Any]] = []

    for schema in database_order:
        entry = database_entries_map.get(schema, {})
        tables_map = entry.get("tables", {})
        db_totals = init_totals()
        clean_tables: Dict[str, Dict[str, Any]] = {}
        for table in sorted(tables_map.keys()):
            cell = tables_map[table]
            normalized = {
                "schema": schema,
                "table": table,
                "rows": cell.get("rows"),
                "storageBytes": cell.get("storageBytes"),
                "debeziumMinBytes": cell.get("debeziumMinBytes"),
                "debeziumMaxBytes": cell.get("debeziumMaxBytes"),
                "timestampColumn": cell.get("timestampColumn"),
                "query": cell.get("query"),
                "error": cell.get("error"),
            }
            clean_tables[table] = normalized
            accumulate_totals(db_totals, normalized)
            accumulate_totals(overall_totals, normalized)
            if table in table_totals:
                accumulate_totals(table_totals[table], normalized)
                if schema not in table_totals[table]["presentIn"]:
                    table_totals[table]["presentIn"].append(schema)

        database_entries.append(
            {
                "name": schema,
                "tables": clean_tables,
                "totals": db_totals,
            }
        )

    table_entries = [
        {
            "name": table,
            "totals": {
                "rows": totals["rows"],
                "storageBytes": totals["storageBytes"],
                "debeziumMinBytes": totals["debeziumMinBytes"],
                "debeziumMaxBytes": totals["debeziumMaxBytes"],
                "presentIn": totals["presentIn"],
            },
        }
        for table, totals in table_totals.items()
    ]

    dataset = {
        "hours": hours,
        "windowStart": window_start_iso,
        "generatedAt": dt.datetime.utcnow().replace(microsecond=0).isoformat(),
        "databaseOrder": database_order,
        "tableOrder": table_names,
        "databases": database_entries,
        "tables": table_entries,
        "overall": overall_totals,
    }
    return dataset


def merge_datasets(
    existing: Optional[Dict[str, Any]],
    new: Dict[str, Any],
) -> Dict[str, Any]:
    if not existing:
        return new
    raw_map: Dict[str, Dict[str, Any]] = {}
    for dataset in (existing, new):
        for db in dataset.get("databases", []):
            raw_map[db["name"]] = db
    hours = new.get("hours") or existing.get("hours") or 48
    window_start = new.get("windowStart") or existing.get("windowStart")
    if not window_start:
        window_start = dt.datetime.utcnow().replace(microsecond=0).isoformat()
    return rebuild_dataset_from_entries(raw_map, hours, window_start)


def render_console_report(aggregated: List[AggregatedStats], hours: int) -> None:
    if not aggregated:
        print("No eligible schemas found for reporting.")
        return
    for schema_stats in aggregated:
        print(f"\nSchema: {schema_stats.schema}")
        if not schema_stats.tables:
            print("  (无 creat* 时间字段的表，已跳过)")
            continue
        for table_stats in sorted(schema_stats.tables, key=lambda item: item.table):
            print(format_table(table_stats))
        total_rows = sum(stat.row_count or 0 for stat in schema_stats.tables)
        storage_total = sum(stat.storage_bytes_estimate or 0.0 for stat in schema_stats.tables)
        debezium_min_total = sum(stat.debezium_min_bytes or 0.0 for stat in schema_stats.tables)
        debezium_max_total = sum(stat.debezium_max_bytes or 0.0 for stat in schema_stats.tables)
        print(f"  -> Total rows (approx last {hours}h): {total_rows}")
        print(
            "  -> Estimated storage volume: "
            f"{human_readable_bytes(storage_total)}"
        )
        print(
            "  -> Estimated Debezium volume: "
            f"{human_readable_bytes(debezium_min_total)}~"
            f"{human_readable_bytes(debezium_max_total)}"
        )


def render_html_report(dataset: Dict[str, Any], output_path: Path) -> None:
    output_path = output_path.resolve()
    output_path.parent.mkdir(parents=True, exist_ok=True)
    json_payload = json.dumps(dataset)
    window_start = html.escape(dataset.get("windowStart", ""))
    generated_at = html.escape(dataset.get("generatedAt", ""))
    hours_text = str(dataset.get("hours", ""))

    def safe_int(value: Optional[Any]) -> int:
        if value is None:
            return 0
        try:
            return int(value)
        except (TypeError, ValueError):
            return 0

    def format_int(value: Optional[Any]) -> str:
        return f"{safe_int(value):,}"

    def format_range(min_bytes: Optional[float], max_bytes: Optional[float]) -> str:
        if min_bytes is None or max_bytes is None:
            return "-"
        return f"{human_readable_bytes(min_bytes)}~{human_readable_bytes(max_bytes)}"

    def fallback_rows_html() -> str:
        database_entries = dataset.get("databases", [])
        if not database_entries:
            return "<p>尚未收集到任何库表统计信息。</p>"

        order = dataset.get("databaseOrder") or [entry.get("name") for entry in database_entries]
        index = {entry.get("name"): entry for entry in database_entries}

        rows: List[str] = []
        for schema in order:
            entry = index.get(schema)
            if not entry:
                continue
            totals = entry.get("totals", {})
            # 简化显示格式，只显示指定的内容
            summary = (
                f"行数: {format_int(totals.get('rows'))}"
                + " | 流量: "
                + format_range(totals.get("debeziumMinBytes"), totals.get("debeziumMaxBytes"))
                + " | 存储: "
                + human_readable_bytes(totals.get("storageBytes"))
            )
            rows.append(
                "<tr><th>"
                + html.escape(schema)
                + "</th><td>"
                + summary
                + "</td></tr>"
            )
        return (
            "<table class=\"fallback-table\"><thead><tr><th>数据库</th><th>汇总</th></tr></thead><tbody>"
            + "".join(rows)
            + "</tbody></table>"
        )

    fallback_html = fallback_rows_html()

    default_columns_js = json.dumps(DEFAULT_TABLE_COLUMNS, ensure_ascii=False)

    template = """<!DOCTYPE html>
<html lang=\"zh-CN\">
<head>
<meta charset=\"utf-8\" />
<meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />
<title>CDC 48 小时容量评估</title>
<!--
占比计算规则：
- 当前行列对应的行数 除以 当前列总行数
- 例如：某数据库某表的行数 / 该表在所有数据库中的总行数
-->
<style>
body { font-family: \"Segoe UI\", Arial, sans-serif; margin: 0; padding: 24px; background: #f8f9fb; color: #1f2933; }
.page-header { margin-bottom: 24px; }
.page-header h1 { margin: 0 0 8px; font-size: 24px; font-weight: 600; }
.page-header .meta { margin: 0; color: #52606d; font-size: 14px; }
.filter-bar { display: grid; grid-template-columns: 1.2fr 1.2fr auto 2fr; gap: 12px; align-items: end; margin-bottom: 20px; }
.filter-item label { display: block; font-weight: 600; margin-bottom: 6px; font-size: 14px; }
.multi-select { position: relative; }
.multi-select-toggle { width: 100%; padding: 10px 12px; border: 1px solid #d0d7de; border-radius: 4px; background: #fff; text-align: left; cursor: pointer; font-size: 14px; color: #1f2933; }
.multi-select.open .multi-select-toggle { border-bottom-left-radius: 0; border-bottom-right-radius: 0; }
.multi-select-panel { display: none; position: absolute; top: 100%; left: 0; width: 100%; background: #fff; border: 1px solid #d0d7de; border-top: none; border-bottom-left-radius: 4px; border-bottom-right-radius: 4px; box-shadow: 0 8px 16px rgba(15, 23, 42, 0.12); z-index: 20; padding: 8px; max-height: 280px; overflow-y: auto; }
.multi-select.open .multi-select-panel { display: block; }
.multi-select-actions { display: flex; justify-content: space-between; margin-bottom: 6px; }
.multi-select-actions button { font-size: 12px; background: none; border: none; color: #2563eb; cursor: pointer; padding: 4px; }
.multi-select-search { width: 100%; padding: 6px 8px; border: 1px solid #d0d7de; border-radius: 4px; margin-bottom: 6px; font-size: 13px; }
.multi-select-option { display: flex; align-items: center; gap: 8px; padding: 4px 0; font-size: 13px; color: #1f2933; }
.multi-select-option input { margin: 0; }
.primary-btn { padding: 10px 20px; background: #2563eb; color: #fff; border: none; border-radius: 4px; font-size: 14px; cursor: pointer; box-shadow: 0 2px 6px rgba(37, 99, 235, 0.3); }
.primary-btn:hover { background: #1d4ed8; }
.primary-btn:disabled { background: #cbd5f5; color: #e0e7ff; cursor: not-allowed; box-shadow: none; }
.summary-box { padding: 12px 16px; background: #fff; border: 1px solid #d0d7de; border-radius: 4px; min-height: 44px; font-weight: 600; display: flex; align-items: center; font-size: 14px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; min-width: 0; }
.table-container { background: #fff; border: 1px solid #d0d7de; border-radius: 6px; box-shadow: 0 12px 24px rgba(15, 23, 42, 0.08); overflow-x: auto; }
table { width: 100%; border-collapse: collapse; min-width: 960px; }
thead th { background: #f1f5f9; font-weight: 600; font-size: 13px; text-transform: uppercase; letter-spacing: 0.04em; color: #334155; padding: 10px; border-bottom: 1px solid #d0d7de; position: sticky; top: 0; z-index: 5; }
tbody th { background: #f8fafc; font-weight: 600; color: #1f2933; }
th, td { border: 1px solid #d0d7de; padding: 8px; vertical-align: top; min-width: 120px; }
tbody tr:nth-child(even) { background: #f8fafc; }
.totals-row { background: #eef2ff; font-weight: 600; }
.cell-content { position: relative; padding-right: 32px; }
.cell-content .lines { display: grid; grid-template-columns: 1fr 1fr; gap: 4px 8px; font-size: 11px; color: #1f2933; line-height: 1.1; }
.cell-content .line-secondary { color: #52606d; font-size: 12px; }
.cell-content .line-error { color: #b91c1c; font-size: 12px; }
.cell-content .cell-rows { color: #0066cc; font-weight: 600; white-space: nowrap; }
.cell-content .cell-percentage { color: #6600cc; font-weight: 600; white-space: nowrap; }
.cell-content .cell-traffic { color: #cc6600; font-weight: 600; white-space: nowrap; }
.cell-content .cell-storage { color: #009900; font-weight: 600; white-space: nowrap; }
.cell-content .cell-rows.warning { color: #cc0000; }
.cell-content .cell-percentage.warning { color: #cc0000; }
.cell-content .cell-traffic.warning { color: #cc0000; }
.cell-content .cell-zero { color: #999999; }
.copy-btn { position: absolute; top: 6px; right: 6px; border: none; background: transparent; cursor: pointer; padding: 0; }
.copy-btn svg { width: 16px; height: 16px; fill: #475569; }
.copy-btn:hover svg { fill: #1d4ed8; }
.copy-btn:disabled { cursor: not-allowed; }
.copy-btn:disabled svg { fill: #cbd5f5; }
.copy-feedback { position: absolute; top: 6px; right: 32px; font-size: 12px; color: #16a34a; opacity: 0; transition: opacity 0.3s ease; }
.copy-feedback.visible { opacity: 1; }
.empty-state { margin-top: 16px; padding: 20px; background: #fff; border: 1px dashed #cbd5f5; border-radius: 6px; color: #52606d; font-size: 14px; text-align: center; }
.sr-only { position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); white-space: nowrap; border: 0; }
.fallback { margin-top: 16px; padding: 16px; background: #fff; border: 1px solid #d0d7de; border-radius: 6px; box-shadow: 0 6px 12px rgba(15, 23, 42, 0.05); }
.fallback h2 { margin: 0 0 12px; font-size: 18px; }
.fallback-table { width: 100%; border-collapse: collapse; }
.fallback-table th, .fallback-table td { border: 1px solid #d0d7de; padding: 8px; vertical-align: top; font-size: 13px; }
.fallback-table th { width: 180px; background: #f1f5f9; }
.fallback-table ul { margin: 6px 0 0 20px; padding: 0; }
.fallback-table li { margin: 0 0 4px; }
/* 帮助图标和模态框样式 */
.help-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: #0969da;
  color: white;
  border-radius: 50%;
  text-align: center;
  line-height: 20px;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  margin-left: 8px;
  vertical-align: middle;
}
.help-icon:hover { background: #0860ca; }

.help-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-modal-content {
  background: white;
  border-radius: 8px;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.help-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #d1d9e0;
}

.help-modal-header h3 {
  margin: 0;
  font-size: 18px;
  color: #24292f;
}

.help-modal-close {
  font-size: 24px;
  cursor: pointer;
  color: #656d76;
  line-height: 1;
}
.help-modal-close:hover { color: #24292f; }

.help-modal-body {
  padding: 20px 24px;
}

.help-modal-body h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #24292f;
}

.help-modal-body ul {
  margin: 0 0 20px 0;
  padding-left: 20px;
}

.help-modal-body li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.example-data {
  background: #f6f8fa;
  border: 1px solid #d1d9e0;
  border-radius: 6px;
  padding: 16px;
}

.example-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.example-item:last-child {
  margin-bottom: 0;
}

.example-label {
  font-weight: 600;
  color: #24292f;
}

.example-value {
  font-family: monospace;
  font-weight: bold;
}

.example-value.rows { color: #0969da; }
.example-value.percentage { color: #8250df; }
.example-value.traffic { color: #cf222e; }
.example-value.storage { color: #1a7f37; }

@media (max-width: 1024px) {
  .filter-bar { grid-template-columns: 1fr; }
  .filter-bar .filter-item { width: 100%; }
  .table-container { overflow-x: auto; }
  .help-modal-content { margin: 20px; max-width: none; }
}
</style>
</head>
<body>
  <header class=\"page-header\">
    <h1>CDC 48 小时容量评估
      <span class=\"help-icon\" id=\"help-icon\" title=\"点击查看计算方式\">?</span>
    </h1>
    <p class=\"meta\">窗口起始：__WINDOW_START__ · 生成时间：__GENERATED_AT__ · 统计范围：近 __HOURS__ 小时</p>
  </header>

  <div id=\"help-modal\" class=\"help-modal\" style=\"display: none;\">
    <div class=\"help-modal-content\">
      <div class=\"help-modal-header\">
        <h3>数据计算方式说明</h3>
        <span class=\"help-modal-close\" id=\"help-modal-close\">&times;</span>
      </div>
      <div class=\"help-modal-body\">
        <h4>字段说明：</h4>
        <ul>
          <li><strong>行数</strong>: 在指定时间窗口内新增的记录数量</li>
          <li><strong>占比</strong>: 当前行列对应的行数 除以 当前列总行数</li>
          <li><strong>流量</strong>: Debezium CDC 传输的网络流量范围（最小值~最大值）</li>
          <li><strong>存储</strong>: 数据在数据库中的实际存储大小</li>
        </ul>
        <h4>示例数据：</h4>
        <div class=\"example-data\">
          <div class=\"example-item\">
            <span class=\"example-label\">行数:</span>
            <span class=\"example-value rows\">14,467</span>
          </div>
          <div class=\"example-item\">
            <span class=\"example-label\">占比:</span>
            <span class=\"example-value percentage\">100.00%</span>
          </div>
          <div class=\"example-item\">
            <span class=\"example-label\">流量:</span>
            <span class=\"example-value traffic\">434.59~542.14 MB</span>
          </div>
          <div class=\"example-item\">
            <span class=\"example-label\">存储:</span>
            <span class=\"example-value storage\">537.72 MB</span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <section class=\"filter-bar\">
    <div class=\"filter-item\">
      <label for=\"database-toggle\">数据库</label>
      <div class=\"multi-select\" id=\"database-filter\" data-title=\"数据库\">
        <button type=\"button\" class=\"multi-select-toggle\" id=\"database-toggle\">加载中…</button>
        <div class=\"multi-select-panel\">
          <div class=\"multi-select-actions\">
            <button type=\"button\" data-action=\"select-all\">全选</button>
            <button type=\"button\" data-action=\"clear-all\">清空</button>
          </div>
          <input type=\"text\" class=\"multi-select-search\" placeholder=\"搜索数据库\" />
          <div class=\"multi-select-options\"></div>
        </div>
      </div>
    </div>
    <div class=\"filter-item\">
      <label for=\"table-toggle\">表名</label>
      <div class=\"multi-select\" id=\"table-filter\" data-title=\"表\">
        <button type=\"button\" class=\"multi-select-toggle\" id=\"table-toggle\">加载中…</button>
        <div class=\"multi-select-panel\">
          <div class=\"multi-select-actions\">
            <button type=\"button\" data-action=\"select-all\">全选</button>
            <button type=\"button\" data-action=\"clear-all\">清空</button>
          </div>
          <input type=\"text\" class=\"multi-select-search\" placeholder=\"搜索表\" />
          <div class=\"multi-select-options\"></div>
        </div>
      </div>
    </div>
    <div class=\"filter-item\" style=\"display:flex; align-items:flex-end;\">
      <button type=\"button\" class=\"primary-btn\" id=\"apply-filters\">确定</button>
    </div>
    <div class=\"filter-item\">
      <div class=\"summary-box\" id=\"filter-summary\">行数: 0 | 流量: 0.00~0.00 MB | 存储: 0.00 MB</div>
    </div>
  </section>
  <div id=\"fallback-summary\" class=\"fallback\">
    <h2>预览汇总</h2>
    <p style=\"margin:0 0 12px; color:#52606d; font-size:13px;\">如浏览器未加载交互脚本，可先参考下表的近 48 小时汇总概览。</p>
    __FALLBACK_SUMMARY__
  </div>
  <div class=\"table-container\">
    <table id=\"stats-table\">
      <thead></thead>
      <tbody></tbody>
    </table>
  </div>
  <div id=\"empty-state\" class=\"empty-state\" hidden>请选择至少一个数据库和一张表来查看统计数据。</div>
  <script type=\"application/json\" id=\"dataset-data\">__DATASET_JSON__</script>
<script>
(function() {
  const datasetEl = document.getElementById('dataset-data');
  if (!datasetEl) {
    return;
  }
  const dataset = JSON.parse(datasetEl.textContent || '{}');
  const DEFAULT_TABLE_COLUMNS = __DEFAULT_COLUMNS__;
  const numberFormatter = new Intl.NumberFormat('en-US');
  const fallback = document.getElementById('fallback-summary');

  const databaseMap = new Map((dataset.databases || []).map((db) => [db.name, db]));
  const tablesMap = new Map((dataset.tables || []).map((table) => [table.name, table]));

  function createOptionLabel(name, totals) {
    const rows = totals && typeof totals.rows === 'number' ? totals.rows : 0;
    const sizeRange = formatRangeForFilter(
      totals ? totals.debeziumMinBytes : null,
      totals ? totals.debeziumMaxBytes : null
    );
    return name + '|' + sizeRange + '|' + numberFormatter.format(rows);
  }

  function formatRangeForFilter(minBytes, maxBytes) {
    if (minBytes == null || maxBytes == null) {
      return '0~0M';
    }
    return formatMegabytes(minBytes, 1) + '~' + formatMegabytes(maxBytes, 1) + 'M';
  }

  function formatMegabytes(bytes, digits) {
    const value = bytes / (1024 * 1024);
    return value.toFixed(digits);
  }

  function initMultiSelect(container, options, selectedSet) {
    const toggle = container.querySelector('.multi-select-toggle');
    const panel = container.querySelector('.multi-select-panel');
    const optionsContainer = container.querySelector('.multi-select-options');
    const searchInput = container.querySelector('.multi-select-search');
    const selectAllBtn = container.querySelector('[data-action="select-all"]');
    const clearAllBtn = container.querySelector('[data-action="clear-all"]');

    let isOpen = false;

    function updateToggleText() {
      const selected = Array.from(selectedSet);
      if (selected.length === 0) {
        toggle.textContent = '请选择...';
      } else if (selected.length === 1) {
        toggle.textContent = selected[0];
      } else {
        toggle.textContent = `已选择 ${selected.length} 项`;
      }
    }

    function renderOptions(filterText = '') {
      optionsContainer.innerHTML = '';
      const filteredOptions = options.filter(opt =>
        opt.value.toLowerCase().includes(filterText.toLowerCase())
      );

      filteredOptions.forEach(option => {
        const div = document.createElement('div');
        div.className = 'multi-select-option';

        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.checked = selectedSet.has(option.value);
        checkbox.addEventListener('change', () => {
          if (checkbox.checked) {
            selectedSet.add(option.value);
          } else {
            selectedSet.delete(option.value);
          }
          updateToggleText();
        });

        const label = document.createElement('label');
        label.textContent = option.value;
        label.style.cursor = 'pointer';
        label.addEventListener('click', () => {
          checkbox.checked = !checkbox.checked;
          checkbox.dispatchEvent(new Event('change'));
        });

        div.appendChild(checkbox);
        div.appendChild(label);
        optionsContainer.appendChild(div);
      });
    }

    toggle.addEventListener('click', () => {
      isOpen = !isOpen;
      container.classList.toggle('open', isOpen);
      if (isOpen) {
        renderOptions();
        searchInput.focus();
      }
    });

    searchInput.addEventListener('input', (e) => {
      renderOptions(e.target.value);
    });

    selectAllBtn.addEventListener('click', () => {
      options.forEach(opt => selectedSet.add(opt.value));
      renderOptions(searchInput.value);
      updateToggleText();
    });

    clearAllBtn.addEventListener('click', () => {
      selectedSet.clear();
      renderOptions(searchInput.value);
      updateToggleText();
    });

    document.addEventListener('click', (e) => {
      if (!container.contains(e.target)) {
        isOpen = false;
        container.classList.remove('open');
      }
    });

    updateToggleText();
    return {
      updateToggleText,
      getSelected() {
        return new Set(selectedSet);
      }
    };
  }

  function formatPercent(value) {
    if (value == null) return '-';
    return (value * 100).toFixed(1) + '%';
  }

  function formatRangeForCell(minBytes, maxBytes) {
    if (minBytes == null || maxBytes == null) {
      return '0 B~0 B';
    }
    return formatStorage(minBytes) + '~' + formatStorage(maxBytes);
  }

  function formatStorage(bytes) {
    if (bytes == null || bytes === 0) return '0 B';
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    return size.toFixed(2) + ' ' + units[unitIndex];
  }

  function initTotals() {
    return {
      rows: 0,
      storageBytes: 0,
      debeziumMinBytes: 0,
      debeziumMaxBytes: 0,
    };
  }

  const defaultTableSelection = (DEFAULT_TABLE_COLUMNS || []).filter((name) => tablesMap.has(name));
  const tableOptions = [];
  (DEFAULT_TABLE_COLUMNS || []).forEach((name) => {
    const table = tablesMap.get(name);
    if (!table) {
      return;
    }
    tableOptions.push({ value: name, label: createOptionLabel(name, table.totals) });
  });
  (dataset.tables || []).forEach((table) => {
    if ((DEFAULT_TABLE_COLUMNS || []).indexOf(table.name) !== -1) {
      return;
    }
    tableOptions.push({ value: table.name, label: createOptionLabel(table.name, table.totals) });
  });

  // Cookie 操作函数
  function setCookie(name, value, days = 30) {
    const expires = new Date();
    expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
    document.cookie = `${name}=${encodeURIComponent(JSON.stringify(value))};expires=${expires.toUTCString()};path=/`;
  }

  function getCookie(name) {
    const nameEQ = name + "=";
    const ca = document.cookie.split(';');
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i];
      while (c.charAt(0) === ' ') c = c.substring(1, c.length);
      if (c.indexOf(nameEQ) === 0) {
        try {
          return JSON.parse(decodeURIComponent(c.substring(nameEQ.length, c.length)));
        } catch (e) {
          return null;
        }
      }
    }
    return null;
  }

  // 默认排除的数据库
  const excludedDatabases = new Set(['adappalyze_6447234445_auth_4', 'offline_music_3_test']);

  // 默认选择的表
  const defaultTables = new Set([
    'account_transactions',
    'accounts',
    'apple_push_logs',
    'apple_server_notifications',
    'devices',
    'experiment_group_map_users',
    'experiment_groups',
    'experiment_white_users',
    'experiments',
    'iad_attributions',
    'remote_action_map_users',
    'remote_actions',
    'target_config_rule_match_users',
    'target_config_rules',
    'targeting_configs',
    'traffic_layers',
    'visit_logs'
  ]);

  // 从Cookie加载或使用默认选择
  const savedDatabases = getCookie('selectedDatabases');
  const savedTables = getCookie('selectedTables');

  const defaultDatabaseSelection = savedDatabases ||
    (dataset.databaseOrder || []).filter(dbName => !excludedDatabases.has(dbName));
  const finalTableSelection = savedTables ||
    Array.from(defaultTables).filter(tableName => tablesMap.has(tableName));

  const state = {
    selectedDatabases: new Set(defaultDatabaseSelection),
    selectedTables: new Set(finalTableSelection.length ? finalTableSelection : (dataset.tableOrder || [])),
  };

  const databaseSelect = initMultiSelect(
    document.getElementById('database-filter'),
    (dataset.databases || []).map((db) => ({
      value: db.name,
      label: createOptionLabel(db.name, db.totals),
    })),
    state.selectedDatabases
  );

  const tableSelect = initMultiSelect(
    document.getElementById('table-filter'),
    tableOptions,
    state.selectedTables
  );

  const applyBtn = document.getElementById('apply-filters');
  const summaryEl = document.getElementById('filter-summary');
  const tableContainer = document.querySelector('.table-container');
  const tableElement = document.getElementById('stats-table');
  const tableHead = tableElement.querySelector('thead');
  const tableBody = tableElement.querySelector('tbody');
  const emptyState = document.getElementById('empty-state');

  function initTotals() {
    return { rows: 0, storageBytes: 0, debeziumMinBytes: 0, debeziumMaxBytes: 0 };
  }

  function createOptionLabel(name, totals) {
    const rows = totals && typeof totals.rows === 'number' ? totals.rows : 0;
    const sizeRange = formatRangeForFilter(
      totals ? totals.debeziumMinBytes : null,
      totals ? totals.debeziumMaxBytes : null
    );
    return name + '|' + sizeRange + '|' + numberFormatter.format(rows);
  }

  function formatRangeForFilter(minBytes, maxBytes) {
    if (minBytes == null || maxBytes == null) {
      return '0~0M';
    }
    return formatMegabytes(minBytes, 1) + '~' + formatMegabytes(maxBytes, 1) + 'M';
  }

  function formatMegabytes(bytes, digits) {
    const value = bytes / (1024 * 1024);
    return value.toFixed(digits);
  }

  function formatRangeForCell(minBytes, maxBytes) {
    if (minBytes == null || maxBytes == null) {
      return '-';
    }
    return formatMegabytes(minBytes, 2) + '~' + formatMegabytes(maxBytes, 2) + ' MB';
  }

  function formatStorage(bytes) {
    if (bytes == null) {
      return '-';
    }
    return formatMegabytes(bytes, 2) + ' MB';
  }

  function formatPercent(value) {
    if (value == null || !isFinite(value)) {
      return '-';
    }
    return (value * 100).toFixed(2) + '%';
  }

  function accumulateTotals(target, cell) {
    if (!cell) {
      return;
    }
    if (typeof cell.rows === 'number') {
      target.rows += cell.rows;
    }
    if (typeof cell.storageBytes === 'number') {
      target.storageBytes += cell.storageBytes;
    }
    if (typeof cell.debeziumMinBytes === 'number') {
      target.debeziumMinBytes += cell.debeziumMinBytes;
    }
    if (typeof cell.debeziumMaxBytes === 'number') {
      target.debeziumMaxBytes += cell.debeziumMaxBytes;
    }
  }

  function computeSummary(selectedDbNames, selectedTableNames) {
    const grand = initTotals();
    const perDatabase = {};
    const perTable = {};
    selectedDbNames.forEach((name) => {
      perDatabase[name] = initTotals();
    });
    selectedTableNames.forEach((table) => {
      perTable[table] = initTotals();
    });

    (dataset.databases || []).forEach((db) => {
      if (!perDatabase[db.name]) {
        return;
      }
      const dbTotals = perDatabase[db.name];
      const cellMap = db.tables || {};
      selectedTableNames.forEach((tableName) => {
        const cell = cellMap[tableName];
        if (!cell) {
          return;
        }
        accumulateTotals(dbTotals, cell);
        accumulateTotals(grand, cell);
        accumulateTotals(perTable[tableName], cell);
      });
    });

    return { grand, perDatabase, perTable, selectedDbNames, selectedTableNames };
  }

  function updateSummary(summary) {
    const grand = summary.grand;
    const summaryText = `行数: ${numberFormatter.format(grand.rows)} | 流量: ${formatRangeForCell(grand.debeziumMinBytes, grand.debeziumMaxBytes)} | 存储: ${formatStorage(grand.storageBytes)}`;
    summaryEl.textContent = summaryText;
  }

  function copySql(query, feedbackEl) {
    const showFeedback = () => {
      feedbackEl.classList.add('visible');
      setTimeout(() => feedbackEl.classList.remove('visible'), 1200);
    };
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard.writeText(query).then(showFeedback).catch(() => fallbackCopy(query, feedbackEl, showFeedback));
    } else {
      fallbackCopy(query, feedbackEl, showFeedback);
    }
  }

  function fallbackCopy(query, feedbackEl, onSuccess) {
    const textarea = document.createElement('textarea');
    textarea.value = query;
    textarea.style.position = 'fixed';
    textarea.style.opacity = '0';
    document.body.appendChild(textarea);
    textarea.select();
    try {
      const successful = document.execCommand('copy');
      if (successful) {
        onSuccess();
      } else {
        alert('无法复制，请手动复制 SQL:\\n' + query);
      }
    } catch (err) {
      alert('无法复制，请手动复制 SQL:\\n' + query);
    } finally {
      document.body.removeChild(textarea);
    }
  }



  function renderTable(selectedDbNames, selectedTableNames) {
    // 清空表格
    tableHead.innerHTML = '';
    tableBody.innerHTML = '';

    if (selectedDbNames.length === 0 || selectedTableNames.length === 0) {
      tableContainer.style.display = 'none';
      if (emptyState) emptyState.hidden = false;
      if (fallback) fallback.style.display = 'block';
      return;
    }

    tableContainer.style.display = 'block';
    if (emptyState) emptyState.hidden = true;
    if (fallback) fallback.style.display = 'none';

    // 创建表头
    const headerRow = document.createElement('tr');
    const dbHeader = document.createElement('th');
    dbHeader.textContent = '数据库';
    headerRow.appendChild(dbHeader);

    const totalHeader = document.createElement('th');
    totalHeader.textContent = '汇总';
    headerRow.appendChild(totalHeader);

    selectedTableNames.forEach((tableName) => {
      const th = document.createElement('th');
      th.textContent = tableName;
      headerRow.appendChild(th);
    });
    tableHead.appendChild(headerRow);

    // 计算汇总数据
    const summary = computeSummary(selectedDbNames, selectedTableNames);

    // 创建汇总行
    const totalsRow = document.createElement('tr');
    totalsRow.className = 'totals-row';

    const totalLabel = document.createElement('th');
    totalLabel.textContent = '汇总';
    totalsRow.appendChild(totalLabel);

    const totalCell = document.createElement('td');
    totalCell.appendChild(createCellElement({
      rows: summary.grand.rows,
      share: 1,
      storageBytes: summary.grand.storageBytes,
      debeziumMinBytes: summary.grand.debeziumMinBytes,
      debeziumMaxBytes: summary.grand.debeziumMaxBytes,
      timestampColumn: null,
      query: null, // 汇总行不需要SQL查询按钮
      error: null,
    }));
    totalsRow.appendChild(totalCell);

    selectedTableNames.forEach((tableName) => {
      const tableTotals = summary.perTable[tableName] || initTotals();
      const cell = document.createElement('td');
      // 汇总行的占比始终为100%（该列总数就是它自己）
      const share = tableTotals.rows > 0 ? 1.0 : 0;
      cell.appendChild(createCellElement({
        rows: tableTotals.rows,
        share,
        storageBytes: tableTotals.storageBytes,
        debeziumMinBytes: tableTotals.debeziumMinBytes,
        debeziumMaxBytes: tableTotals.debeziumMaxBytes,
        timestampColumn: null,
        query: null, // 汇总列不需要SQL查询按钮
        error: null,
      }));
      totalsRow.appendChild(cell);
    });
    tableBody.appendChild(totalsRow);

    // 创建数据库行，按汇总列的占比降序排列
    const sortedDbNames = selectedDbNames.slice().sort((a, b) => {
      const dbTotalsA = summary.perDatabase[a] || initTotals();
      const dbTotalsB = summary.perDatabase[b] || initTotals();

      // 计算汇总列的占比
      const shareA = summary.grand.rows > 0 ? dbTotalsA.rows / summary.grand.rows : 0;
      const shareB = summary.grand.rows > 0 ? dbTotalsB.rows / summary.grand.rows : 0;

      return shareB - shareA; // 降序排列
    });

    sortedDbNames.forEach((dbName) => {
      const db = databaseMap.get(dbName);
      const dbRow = document.createElement('tr');

      const dbHeaderCell = document.createElement('th');
      dbHeaderCell.textContent = dbName;
      dbRow.appendChild(dbHeaderCell);

      const dbTotals = summary.perDatabase[dbName] || initTotals();
      const dbTotalCell = document.createElement('td');
      const dbShare = summary.grand.rows > 0 && dbTotals.rows > 0
        ? dbTotals.rows / summary.grand.rows
        : (dbTotals.rows === 0 ? 0 : null);
      dbTotalCell.appendChild(createCellElement({
        rows: dbTotals.rows,
        share: dbShare,
        storageBytes: dbTotals.storageBytes,
        debeziumMinBytes: dbTotals.debeziumMinBytes,
        debeziumMaxBytes: dbTotals.debeziumMaxBytes,
        timestampColumn: null,
        query: null, // 汇总列不需要SQL查询按钮
        error: null,
      }));
      dbRow.appendChild(dbTotalCell);

      selectedTableNames.forEach((tableName) => {
        const cellData = db && db.tables ? db.tables[tableName] : null;
        const cell = document.createElement('td');
        if (!cellData) {
          cell.appendChild(createCellElement({
            rows: null,
            share: null,
            storageBytes: null,
            debeziumMinBytes: null,
            debeziumMaxBytes: null,
            timestampColumn: null,
            query: 'SELECT "' + dbName + '.' + tableName + '" as table_info, "表不存在或无creat*字段" as status;',
            error: null,
          }));
        } else {
          // 占比计算：当前行列对应的行数 除以 当前列总行数
          const tableTotals = summary.perTable[tableName] || initTotals();
          const shareValue = tableTotals.rows > 0 && typeof cellData.rows === 'number'
            ? cellData.rows / tableTotals.rows
            : (cellData.rows === 0 ? 0 : null);
          cell.appendChild(createCellElement({
            rows: cellData.rows,
            share: shareValue,
            storageBytes: cellData.storageBytes,
            debeziumMinBytes: cellData.debeziumMinBytes,
            debeziumMaxBytes: cellData.debeziumMaxBytes,
            timestampColumn: cellData.timestampColumn,
            query: cellData.query,
            error: cellData.error,
          }));
        }
        dbRow.appendChild(cell);
      });

      tableBody.appendChild(dbRow);
    });

    updateSummary(summary);
    return summary;
  }

  // 帮助模态框事件处理
  const helpIcon = document.getElementById('help-icon');
  const helpModal = document.getElementById('help-modal');
  const helpModalClose = document.getElementById('help-modal-close');

  helpIcon.addEventListener('click', () => {
    helpModal.style.display = 'flex';
  });

  helpModalClose.addEventListener('click', () => {
    helpModal.style.display = 'none';
  });

  helpModal.addEventListener('click', (e) => {
    if (e.target === helpModal) {
      helpModal.style.display = 'none';
    }
  });

  // 初始化显示
  renderTable(Array.from(state.selectedDatabases), Array.from(state.selectedTables));

  // 添加确定按钮事件处理
  applyBtn.addEventListener('click', () => {
    // 添加加载状态
    applyBtn.disabled = true;
    applyBtn.textContent = '处理中...';

    // 使用setTimeout确保UI更新
    setTimeout(() => {
      state.selectedDatabases = databaseSelect.getSelected();
      state.selectedTables = tableSelect.getSelected();

      // 保存选择到Cookie
      setCookie('selectedDatabases', Array.from(state.selectedDatabases));
      setCookie('selectedTables', Array.from(state.selectedTables));

      renderTable(Array.from(state.selectedDatabases), Array.from(state.selectedTables));

      // 恢复按钮状态
      applyBtn.disabled = false;
      applyBtn.textContent = '确定';

      // 显示成功反馈
      const originalText = applyBtn.textContent;
      applyBtn.textContent = '已更新';
      applyBtn.style.background = '#1a7f37';
      setTimeout(() => {
        applyBtn.textContent = originalText;
        applyBtn.style.background = '';
      }, 1000);
    }, 50);
  });

  function createCellElement(config) {
    const {
      rows,
      share,
      storageBytes,
      debeziumMinBytes,
      debeziumMaxBytes,
      timestampColumn,
      query,
      error,
    } = config;

    const wrapper = document.createElement('div');
    wrapper.className = 'cell-content';

    const copyBtn = document.createElement('button');
    copyBtn.type = 'button';
    copyBtn.className = 'copy-btn';
    copyBtn.title = '复制 SQL';
    copyBtn.innerHTML = '<svg viewBox="0 0 16 16" aria-hidden="true"><path d="M3.75 1A1.75 1.75 0 0 0 2 2.75v8.5C2 12.216 2.784 13 3.75 13H5v.75A1.75 1.75 0 0 0 6.75 15h5.5A1.75 1.75 0 0 0 14 13.25v-8.5A1.75 1.75 0 0 0 12.25 3H11V2.75A1.75 1.75 0 0 0 9.25 1zm0 1.5h5.5a.25.25 0 0 1 .25.25V3H6.75A1.75 1.75 0 0 0 5 4.75V12H3.75a.25.25 0 0 1-.25-.25v-8.5a.25.25 0 0 1 .25-.25Zm3 3.25v8.5a.25.25 0 0 0 .25.25h5.5a.25.25 0 0 0 .25-.25v-8.5a.25.25 0 0 0-.25-.25h-5.5a.25.25 0 0 0-.25.25Z"></path></svg><span class="sr-only">复制 SQL</span>';

    const feedback = document.createElement('span');
    feedback.className = 'copy-feedback';
    feedback.textContent = '已复制';

    if (query) {
      // 修改SQL查询，添加结束时间的闭区间
      let modifiedQuery = query;
      if (query.includes('WHERE') && query.includes('>=')) {
        // 计算结束时间
        const startTime = dataset.windowStart;
        const endTime = dataset.generatedAt;

        // 将 >= 'start_time' 替换为 >= 'start_time' AND column <= 'end_time'
        const timeColumnMatch = query.match(/WHERE\\s+`?([^`\\s]+)`?\\s+>=\\s+'([^']+)'/);
        if (timeColumnMatch) {
          const timeColumn = timeColumnMatch[1];
          modifiedQuery = query.replace(
            /WHERE\\s+`?([^`\\s]+)`?\\s+>=\\s+'([^']+)'/,
            `WHERE \\`${timeColumn}\\` >= '${startTime}' AND \\`${timeColumn}\\` <= '${endTime}'`
          );
        }
      }

      copyBtn.addEventListener('click', (event) => {
        event.preventDefault();
        copySql(modifiedQuery, feedback);
      });
    } else {
      copyBtn.disabled = true;
    }

    const lines = document.createElement('div');
    lines.className = 'lines';

    // 延迟检查是否为汇总行列，避免在创建时就判断
    const lineRows = document.createElement('div');
    let rowsClass = 'cell-rows';
    if (typeof rows === 'number') {
      if (rows === 0) {
        rowsClass += ' cell-zero';
      } else if (rows >= 10000) {
        rowsClass += ' warning';
      }
    } else {
      rowsClass += ' cell-zero';
    }
    lineRows.className = rowsClass;
    lineRows.textContent = '行数: ' + (typeof rows === 'number' ? numberFormatter.format(rows) : '-');
    lines.appendChild(lineRows);

    const lineShare = document.createElement('div');
    let shareClass = 'cell-percentage';
    if (share === null || share === undefined || share === 0) {
      shareClass += ' cell-zero';
    } else if (share >= 0.1) {
      shareClass += ' warning';
    }
    lineShare.className = shareClass;
    lineShare.textContent = '占比: ' + formatPercent(share);
    lines.appendChild(lineShare);

    const lineSize = document.createElement('div');
    let trafficClass = 'cell-traffic';
    const maxTrafficMB = debeziumMaxBytes ? debeziumMaxBytes / (1024 * 1024) : 0;
    if (!debeziumMinBytes && !debeziumMaxBytes) {
      trafficClass += ' cell-zero';
    } else if (maxTrafficMB >= 30) {
      trafficClass += ' warning';
    }
    lineSize.className = trafficClass;
    lineSize.textContent = '流量: ' + formatRangeForCell(debeziumMinBytes, debeziumMaxBytes);
    lines.appendChild(lineSize);

    const storageLine = document.createElement('div');
    let storageClass = 'cell-storage';
    if (!storageBytes || storageBytes === 0) {
      storageClass += ' cell-zero';
    }
    storageLine.className = storageClass;
    storageLine.textContent = '存储: ' + formatStorage(storageBytes);
    lines.appendChild(storageLine);

    // 移除时间列显示

    if (error) {
      const errorLine = document.createElement('div');
      errorLine.className = 'line-error';
      errorLine.textContent = '异常: ' + error;
      lines.appendChild(errorLine);
    }

    wrapper.appendChild(copyBtn);
    wrapper.appendChild(feedback);
    wrapper.appendChild(lines);

    // 延迟检查是否为汇总行列，如果是则移除警告样式
    setTimeout(() => {
      const cell = wrapper.closest('td');
      if (!cell) return;

      const row = cell.closest('tr');
      const table = cell.closest('table');
      if (!row || !table) return;

      // 检查是否为汇总行（第一行）
      const isSummaryRow = row === table.querySelector('tbody tr:first-child');

      // 检查是否为汇总列（第一列，排除表头）
      const cellIndex = Array.from(row.children).indexOf(cell);
      const isSummaryCol = cellIndex === 0;

      if (isSummaryRow || isSummaryCol) {
        // 移除所有警告样式
        const warningElements = wrapper.querySelectorAll('.warning');
        warningElements.forEach(el => el.classList.remove('warning'));
      }
    }, 0);

    return wrapper;
  }

})();
</script>
</body>
</html>"""

    fallback_html = fallback_rows_html()

    default_columns_js = json.dumps(DEFAULT_TABLE_COLUMNS, ensure_ascii=False)

    html_content = template.replace("__WINDOW_START__", window_start)
    html_content = html_content.replace("__GENERATED_AT__", generated_at)
    html_content = html_content.replace("__HOURS__", hours_text)
    html_content = html_content.replace("__FALLBACK_SUMMARY__", fallback_html)
    html_content = html_content.replace("__DATASET_JSON__", json_payload)
    html_content = html_content.replace("__DEFAULT_COLUMNS__", default_columns_js)

    output_path.write_text(html_content, encoding="utf-8")


def merge_datasets(
    existing: Optional[Dict[str, Any]],
    new: Dict[str, Any],
) -> Dict[str, Any]:
    if not existing:
        return new
    raw_map: Dict[str, Dict[str, Any]] = {}
    for dataset in (existing, new):
        for db in dataset.get("databases", []):
            raw_map[db["name"]] = db
    hours = new.get("hours") or existing.get("hours") or 48
    window_start = new.get("windowStart") or existing.get("windowStart")
    if not window_start:
        window_start = dt.datetime.utcnow().replace(microsecond=0).isoformat()
    return rebuild_dataset_from_entries(raw_map, hours, window_start)


def render_console_report(aggregated: List[AggregatedStats], hours: int) -> None:
    if not aggregated:
        print("No eligible schemas found for reporting.")
        return
    for schema_stats in aggregated:
        print(f"\nSchema: {schema_stats.schema}")
        if not schema_stats.tables:
            print("  (无 creat* 时间字段的表，已跳过)")
            continue
        for table_stats in sorted(schema_stats.tables, key=lambda item: item.table):
            print(format_table(table_stats))
        total_rows = sum(stat.row_count or 0 for stat in schema_stats.tables)
        storage_total = sum(stat.storage_bytes_estimate or 0.0 for stat in schema_stats.tables)
        debezium_min_total = sum(stat.debezium_min_bytes or 0.0 for stat in schema_stats.tables)
        debezium_max_total = sum(stat.debezium_max_bytes or 0.0 for stat in schema_stats.tables)
        print(f"  -> Total rows (approx last {hours}h): {total_rows}")
        print(
            "  -> Estimated storage volume: "
            f"{human_readable_bytes(storage_total)}"
        )
        print(
            "  -> Estimated Debezium volume: "
            f"{human_readable_bytes(debezium_min_total)}~"
            f"{human_readable_bytes(debezium_max_total)}"
        )


def run_scan(
    dsn: str,
    hours: int,
    html_path: Optional[Path],
    schema_filters: Optional[List[str]] = None,
) -> Dict[str, Any]:
    window_start = dt.datetime.utcnow() - dt.timedelta(hours=hours)
    scanner = MySQLScanner(dsn)
    progress: Optional[ProgressLogger] = None
    aggregated: List[AggregatedStats] = []
    try:
        schemas = scanner.schemas_with_table(TARGET_TABLE)
        if schema_filters:
            wanted = {name.strip() for name in schema_filters if name.strip()}
            schemas = [schema for schema in schemas if schema in wanted]
        if not schemas:
            message = (
                f"No schemas contain table `{TARGET_TABLE}`. Nothing to report."
                if not schema_filters
                else (
                    "No filtered schemas contain table "
                    f"`{TARGET_TABLE}`. Filters: {', '.join(schema_filters)}"
                )
            )
            print(message, file=sys.stderr)
            return {}

        plan_by_schema: Dict[str, List[Tuple[str, str, Dict[str, Optional[float]]]]] = {}
        skip_records: List[Tuple[str, str, str]] = []
        for schema in schemas:
            metadata = scanner.fetch_table_metadata(schema)
            creat_columns = scanner.fetch_creat_columns(schema)
            for table, stats in sorted(metadata.items()):
                if table in SKIP_TABLES:
                    skip_records.append((schema, table, "configured ignore"))
                    continue
                timestamp_column = choose_creat_timestamp_column(creat_columns.get(table, []))
                if not timestamp_column:
                    skip_records.append((schema, table, "无 creat* 时间列"))
                    continue
                plan_by_schema.setdefault(schema, []).append((table, timestamp_column, stats))

        total_tables = sum(len(tables) for tables in plan_by_schema.values())
        log_path = Path(__file__).resolve().with_name("mysql_scan.log")
        skip_doc_path = Path(__file__).resolve().with_name("skipped_tables.md")
        skip_recorder = SkipRecorder(skip_doc_path)
        progress = ProgressLogger(total_tables, log_path, skip_recorder)
        for schema, table, reason in skip_records:
            progress.log_skip(schema, table, reason)

        if total_tables == 0:
            print("没有符合条件的表需要统计。", flush=True)
        for schema in schemas:
            planned_tables = plan_by_schema.get(schema)
            if not planned_tables:
                continue
            aggregated.append(
                scanner.scan_schema(
                    schema,
                    window_start,
                    planned_tables,
                    progress,
                )
            )

        render_console_report(aggregated, hours)
        dataset = build_dataset(aggregated, hours, window_start)
        if html_path is not None:
            render_html_report(dataset, html_path)
            print(f"HTML 报告已生成: {html_path}")
        return dataset
    finally:
        if progress:
            progress.close()
        if 'skip_recorder' in locals() and skip_recorder:
            skip_recorder.flush()
        scanner.close()


def build_arg_parser() -> argparse.ArgumentParser:
    parser = argparse.ArgumentParser(
        description=(
            "Scan MySQL for schemas containing apple_server_notifications and "
            "estimate last-N-hour change volume."
        )
    )
    parser.add_argument(
        "--dsn",
        default=DEFAULT_DSN,
        help="MySQL DSN formatted as user:password@tcp(host:port)",
    )
    parser.add_argument(
        "--hours",
        type=int,
        default=48,
        help="Time window in hours (default: 48)",
    )
    parser.add_argument(
        "--html",
        default="index.html",
        help="Output path for the generated HTML report (default: index.html)",
    )
    parser.add_argument(
        "--schemas",
        help="Comma separated schema list to limit the scan (default: scan all eligible schemas)",
    )
    parser.add_argument(
        "--json",
        help="Optional dataset json path for caching/merging results",
    )
    parser.add_argument(
        "--render-only",
        action="store_true",
        help="仅使用 --json 提供的缓存生成报告，跳过数据库扫描",
    )
    return parser


def main(argv: Optional[Sequence[str]] = None) -> int:
    parser = build_arg_parser()
    args = parser.parse_args(argv)
    try:
        schema_filters = (
            [part for part in args.schemas.split(",") if part.strip()]
            if args.schemas
            else None
        )
        json_path: Optional[Path] = Path(args.json) if args.json else None
        existing_dataset: Optional[Dict[str, Any]] = None
        if json_path and json_path.exists():
            try:
                existing_dataset = json.loads(json_path.read_text(encoding="utf-8"))
            except json.JSONDecodeError as exc:  # pragma: no cover - defensive
                print(f"[WARN] Failed to decode existing JSON cache: {exc}")
                existing_dataset = None

        html_path = Path(args.html) if args.html else None

        if args.render_only:
            if not json_path or existing_dataset is None:
                print("[ERROR] --render-only 需要提供有效的 --json 缓存文件", file=sys.stderr)
                return 1
            if html_path:
                render_html_report(existing_dataset, html_path)
                print(f"汇总 HTML 已生成: {html_path}")
            else:
                print("[WARN] 未指定 --html，已跳过生成 HTML")
            return 0

        dataset = run_scan(args.dsn, args.hours, None if json_path else html_path, schema_filters)
        if json_path:
            dataset = merge_datasets(existing_dataset, dataset)
            json_path.write_text(
                json.dumps(dataset, ensure_ascii=False, indent=2),
                encoding="utf-8",
            )
        if html_path:
            render_html_report(dataset, html_path)
            print(f"汇总 HTML 已生成: {html_path}")
    except Exception as exc:
        print(f"Scan failed: {exc}", file=sys.stderr)
        return 1
    return 0


if __name__ == "__main__":
    sys.exit(main())
