<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>CDC 48 小时容量评估</title>
<style>
body { font-family: "Segoe UI", Arial, sans-serif; margin: 0; padding: 24px; background: #f8f9fb; color: #1f2933; }
.page-header { margin-bottom: 24px; }
.page-header h1 { margin: 0 0 8px; font-size: 24px; font-weight: 600; }
.page-header .meta { margin: 0; color: #52606d; font-size: 14px; }
.filter-bar { display: grid; grid-template-columns: 1.5fr 1.5fr auto 1.4fr; gap: 16px; align-items: start; margin-bottom: 20px; }
.filter-item label { display: block; font-weight: 600; margin-bottom: 6px; font-size: 14px; }
.multi-select { position: relative; }
.multi-select-toggle { width: 100%; padding: 10px 12px; border: 1px solid #d0d7de; border-radius: 4px; background: #fff; text-align: left; cursor: pointer; font-size: 14px; color: #1f2933; }
.multi-select.open .multi-select-toggle { border-bottom-left-radius: 0; border-bottom-right-radius: 0; }
.multi-select-panel { display: none; position: absolute; top: 100%; left: 0; width: 100%; background: #fff; border: 1px solid #d0d7de; border-top: none; border-bottom-left-radius: 4px; border-bottom-right-radius: 4px; box-shadow: 0 8px 16px rgba(15, 23, 42, 0.12); z-index: 20; padding: 8px; max-height: 280px; overflow-y: auto; }
.multi-select.open .multi-select-panel { display: block; }
.multi-select-actions { display: flex; justify-content: space-between; margin-bottom: 6px; }
.multi-select-actions button { font-size: 12px; background: none; border: none; color: #2563eb; cursor: pointer; padding: 4px; }
.multi-select-search { width: 100%; padding: 6px 8px; border: 1px solid #d0d7de; border-radius: 4px; margin-bottom: 6px; font-size: 13px; }
.multi-select-option { display: flex; align-items: center; gap: 8px; padding: 4px 0; font-size: 13px; color: #1f2933; }
.multi-select-option input { margin: 0; }
.primary-btn { padding: 10px 20px; background: #2563eb; color: #fff; border: none; border-radius: 4px; font-size: 14px; cursor: pointer; box-shadow: 0 2px 6px rgba(37, 99, 235, 0.3); }
.primary-btn:hover { background: #1d4ed8; }
.primary-btn:disabled { background: #cbd5f5; color: #e0e7ff; cursor: not-allowed; box-shadow: none; }
.summary-box { padding: 12px 16px; background: #fff; border: 1px solid #d0d7de; border-radius: 4px; min-height: 44px; font-weight: 600; display: flex; align-items: center; font-size: 14px; }
.table-container { background: #fff; border: 1px solid #d0d7de; border-radius: 6px; box-shadow: 0 12px 24px rgba(15, 23, 42, 0.08); overflow-x: auto; }
table { width: 100%; border-collapse: collapse; min-width: 960px; }
thead th { background: #f1f5f9; font-weight: 600; font-size: 13px; text-transform: uppercase; letter-spacing: 0.04em; color: #334155; padding: 10px; border-bottom: 1px solid #d0d7de; position: sticky; top: 0; z-index: 5; }
tbody th { background: #f8fafc; font-weight: 600; color: #1f2933; }
th, td { border: 1px solid #d0d7de; padding: 10px; vertical-align: top; }
tbody tr:nth-child(even) { background: #f8fafc; }
.totals-row { background: #eef2ff; font-weight: 600; }
.cell-content { position: relative; padding-right: 32px; }
.cell-content .lines { display: grid; gap: 4px; font-size: 13px; color: #1f2933; }
.cell-content .line-secondary { color: #52606d; font-size: 12px; }
.cell-content .line-error { color: #b91c1c; font-size: 12px; }
.copy-btn { position: absolute; top: 6px; right: 6px; border: none; background: transparent; cursor: pointer; padding: 0; }
.copy-btn svg { width: 16px; height: 16px; fill: #475569; }
.copy-btn:hover svg { fill: #1d4ed8; }
.copy-btn:disabled { cursor: not-allowed; }
.copy-btn:disabled svg { fill: #cbd5f5; }
.copy-feedback { position: absolute; top: 6px; right: 32px; font-size: 12px; color: #16a34a; opacity: 0; transition: opacity 0.3s ease; }
.copy-feedback.visible { opacity: 1; }
.empty-state { margin-top: 16px; padding: 20px; background: #fff; border: 1px dashed #cbd5f5; border-radius: 6px; color: #52606d; font-size: 14px; text-align: center; }
.sr-only { position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); white-space: nowrap; border: 0; }
@media (max-width: 1024px) {
  .filter-bar { grid-template-columns: 1fr; }
  .filter-bar .filter-item { width: 100%; }
  .table-container { overflow-x: auto; }
}
</style>
</head>
<body>
  <header class="page-header">
    <h1>CDC 48 小时容量评估</h1>
    <p class="meta">窗口起始：2025-09-20T17:46:16 · 生成时间：2025-09-22T17:46:19 · 统计范围：近 48 小时</p>
  </header>
  <section class="filter-bar">
    <div class="filter-item">
      <label for="database-toggle">数据库</label>
      <div class="multi-select" id="database-filter" data-title="数据库">
        <button type="button" class="multi-select-toggle" id="database-toggle">加载中…</button>
        <div class="multi-select-panel">
          <div class="multi-select-actions">
            <button type="button" data-action="select-all">全选</button>
            <button type="button" data-action="clear-all">清空</button>
          </div>
          <input type="text" class="multi-select-search" placeholder="搜索数据库" />
          <div class="multi-select-options"></div>
        </div>
      </div>
    </div>
    <div class="filter-item">
      <label for="table-toggle">表名</label>
      <div class="multi-select" id="table-filter" data-title="表">
        <button type="button" class="multi-select-toggle" id="table-toggle">加载中…</button>
        <div class="multi-select-panel">
          <div class="multi-select-actions">
            <button type="button" data-action="select-all">全选</button>
            <button type="button" data-action="clear-all">清空</button>
          </div>
          <input type="text" class="multi-select-search" placeholder="搜索表" />
          <div class="multi-select-options"></div>
        </div>
      </div>
    </div>
    <div class="filter-item" style="display:flex; align-items:flex-end;">
      <button type="button" class="primary-btn" id="apply-filters">确定</button>
    </div>
    <div class="filter-item">
      <div class="summary-box" id="filter-summary">行数: 0 | 大小(网络): 0.00~0.00 MB | 大小(存储): 0.00 MB</div>
    </div>
  </section>
  <div class="table-container">
    <table id="stats-table">
      <thead></thead>
      <tbody></tbody>
    </table>
  </div>
  <div id="empty-state" class="empty-state" hidden>请选择至少一个数据库和一张表来查看统计数据。</div>
  <script type="application/json" id="dataset-data">{"hours": 48, "windowStart": "2025-09-20T17:46:16", "generatedAt": "2025-09-22T17:46:19", "databaseOrder": ["camera_detector"], "tableOrder": ["app_events", "app_users", "apple_push_task", "apple_server_notifications", "auto_track_receipt_tasks", "experiment_group_map_users", "experiment_groups", "experiment_tests", "experiments", "iad_attributions", "iap_transactions", "target_config_rules", "visit_logs"], "databases": [{"name": "camera_detector", "totals": {"rows": 3689, "storageBytes": 924673.0, "debeziumMinBytes": 1920218.4000000001, "debeziumMaxBytes": 2105153.0}, "tables": {"app_events": {"schema": "camera_detector", "table": "app_events", "rows": 0, "storageBytes": 0.0, "debeziumMinBytes": 0.0, "debeziumMaxBytes": 0.0, "timestampColumn": "created_at", "query": "SELECT COUNT(*) AS row_count FROM `camera_detector`.`app_events` WHERE `created_at` >= '2025-09-20 17:46:16'", "error": null}, "app_users": {"schema": "camera_detector", "table": "app_users", "rows": 5, "storageBytes": 1980.0, "debeziumMinBytes": 3184.0, "debeziumMaxBytes": 3580.0, "timestampColumn": "created_at", "query": "SELECT COUNT(*) AS row_count FROM `camera_detector`.`app_users` WHERE `created_at` >= '2025-09-20 17:46:16'", "error": null}, "apple_push_task": {"schema": "camera_detector", "table": "apple_push_task", "rows": 0, "storageBytes": 0.0, "debeziumMinBytes": 0.0, "debeziumMaxBytes": 0.0, "timestampColumn": "created_at", "query": "SELECT COUNT(*) AS row_count FROM `camera_detector`.`apple_push_task` WHERE `created_at` >= '2025-09-20 17:46:16'", "error": null}, "apple_server_notifications": {"schema": "camera_detector", "table": "apple_server_notifications", "rows": 2, "storageBytes": 54418.0, "debeziumMinBytes": 44174.4, "debeziumMaxBytes": 55058.0, "timestampColumn": "created_at", "query": "SELECT COUNT(*) AS row_count FROM `camera_detector`.`apple_server_notifications` WHERE `created_at` >= '2025-09-20 17:46:16'", "error": null}, "auto_track_receipt_tasks": {"schema": "camera_detector", "table": "auto_track_receipt_tasks", "rows": 31, "storageBytes": 5704.0, "debeziumMinBytes": 14483.2, "debeziumMaxBytes": 15624.0, "timestampColumn": "created_at", "query": "SELECT COUNT(*) AS row_count FROM `camera_detector`.`auto_track_receipt_tasks` WHERE `created_at` >= '2025-09-20 17:46:16'", "error": null}, "experiment_group_map_users": {"schema": "camera_detector", "table": "experiment_group_map_users", "rows": 0, "storageBytes": 0.0, "debeziumMinBytes": 0.0, "debeziumMaxBytes": 0.0, "timestampColumn": "created_at", "query": "SELECT COUNT(*) AS row_count FROM `camera_detector`.`experiment_group_map_users` WHERE `created_at` >= '2025-09-20 17:46:16'", "error": null}, "experiment_groups": {"schema": "camera_detector", "table": "experiment_groups", "rows": 0, "storageBytes": 0.0, "debeziumMinBytes": 0.0, "debeziumMaxBytes": 0.0, "timestampColumn": "created_at", "query": "SELECT COUNT(*) AS row_count FROM `camera_detector`.`experiment_groups` WHERE `created_at` >= '2025-09-20 17:46:16'", "error": null}, "experiment_tests": {"schema": "camera_detector", "table": "experiment_tests", "rows": 0, "storageBytes": 0.0, "debeziumMinBytes": 0.0, "debeziumMaxBytes": 0.0, "timestampColumn": "created_at", "query": "SELECT COUNT(*) AS row_count FROM `camera_detector`.`experiment_tests` WHERE `created_at` >= '2025-09-20 17:46:16'", "error": null}, "experiments": {"schema": "camera_detector", "table": "experiments", "rows": 0, "storageBytes": 0.0, "debeziumMinBytes": 0.0, "debeziumMaxBytes": 0.0, "timestampColumn": "created_at", "query": "SELECT COUNT(*) AS row_count FROM `camera_detector`.`experiments` WHERE `created_at` >= '2025-09-20 17:46:16'", "error": null}, "iad_attributions": {"schema": "camera_detector", "table": "iad_attributions", "rows": 17, "storageBytes": 4726.0, "debeziumMinBytes": 9220.8, "debeziumMaxBytes": 10166.0, "timestampColumn": "created_at", "query": "SELECT COUNT(*) AS row_count FROM `camera_detector`.`iad_attributions` WHERE `created_at` >= '2025-09-20 17:46:16'", "error": null}, "iap_transactions": {"schema": "camera_detector", "table": "iap_transactions", "rows": 1, "storageBytes": 457.0, "debeziumMinBytes": 685.6, "debeziumMaxBytes": 777.0, "timestampColumn": "created_at", "query": "SELECT COUNT(*) AS row_count FROM `camera_detector`.`iap_transactions` WHERE `created_at` >= '2025-09-20 17:46:16'", "error": null}, "target_config_rules": {"schema": "camera_detector", "table": "target_config_rules", "rows": 0, "storageBytes": 0.0, "debeziumMinBytes": 0.0, "debeziumMaxBytes": 0.0, "timestampColumn": "created_at", "query": "SELECT COUNT(*) AS row_count FROM `camera_detector`.`target_config_rules` WHERE `created_at` >= '2025-09-20 17:46:16'", "error": null}, "visit_logs": {"schema": "camera_detector", "table": "visit_logs", "rows": 3633, "storageBytes": 857388.0, "debeziumMinBytes": 1848470.4000000001, "debeziumMaxBytes": 2019948.0, "timestampColumn": "created_at", "query": "SELECT COUNT(*) AS row_count FROM `camera_detector`.`visit_logs` WHERE `created_at` >= '2025-09-20 17:46:16'", "error": null}}}], "tables": [{"name": "app_events", "totals": {"rows": 0, "storageBytes": 0.0, "debeziumMinBytes": 0.0, "debeziumMaxBytes": 0.0, "presentIn": ["camera_detector"]}}, {"name": "app_users", "totals": {"rows": 5, "storageBytes": 1980.0, "debeziumMinBytes": 3184.0, "debeziumMaxBytes": 3580.0, "presentIn": ["camera_detector"]}}, {"name": "apple_push_task", "totals": {"rows": 0, "storageBytes": 0.0, "debeziumMinBytes": 0.0, "debeziumMaxBytes": 0.0, "presentIn": ["camera_detector"]}}, {"name": "apple_server_notifications", "totals": {"rows": 2, "storageBytes": 54418.0, "debeziumMinBytes": 44174.4, "debeziumMaxBytes": 55058.0, "presentIn": ["camera_detector"]}}, {"name": "auto_track_receipt_tasks", "totals": {"rows": 31, "storageBytes": 5704.0, "debeziumMinBytes": 14483.2, "debeziumMaxBytes": 15624.0, "presentIn": ["camera_detector"]}}, {"name": "experiment_group_map_users", "totals": {"rows": 0, "storageBytes": 0.0, "debeziumMinBytes": 0.0, "debeziumMaxBytes": 0.0, "presentIn": ["camera_detector"]}}, {"name": "experiment_groups", "totals": {"rows": 0, "storageBytes": 0.0, "debeziumMinBytes": 0.0, "debeziumMaxBytes": 0.0, "presentIn": ["camera_detector"]}}, {"name": "experiment_tests", "totals": {"rows": 0, "storageBytes": 0.0, "debeziumMinBytes": 0.0, "debeziumMaxBytes": 0.0, "presentIn": ["camera_detector"]}}, {"name": "experiments", "totals": {"rows": 0, "storageBytes": 0.0, "debeziumMinBytes": 0.0, "debeziumMaxBytes": 0.0, "presentIn": ["camera_detector"]}}, {"name": "iad_attributions", "totals": {"rows": 17, "storageBytes": 4726.0, "debeziumMinBytes": 9220.8, "debeziumMaxBytes": 10166.0, "presentIn": ["camera_detector"]}}, {"name": "iap_transactions", "totals": {"rows": 1, "storageBytes": 457.0, "debeziumMinBytes": 685.6, "debeziumMaxBytes": 777.0, "presentIn": ["camera_detector"]}}, {"name": "target_config_rules", "totals": {"rows": 0, "storageBytes": 0.0, "debeziumMinBytes": 0.0, "debeziumMaxBytes": 0.0, "presentIn": ["camera_detector"]}}, {"name": "visit_logs", "totals": {"rows": 3633, "storageBytes": 857388.0, "debeziumMinBytes": 1848470.4000000001, "debeziumMaxBytes": 2019948.0, "presentIn": ["camera_detector"]}}], "overall": {"rows": 3689, "storageBytes": 924673.0, "debeziumMinBytes": 1920218.4000000001, "debeziumMaxBytes": 2105153.0}}</script>
  <script>
(function() {
  const datasetEl = document.getElementById('dataset-data');
  if (!datasetEl) {
    return;
  }
  const dataset = JSON.parse(datasetEl.textContent || '{}');
  const numberFormatter = new Intl.NumberFormat('en-US');

  const databaseMap = new Map((dataset.databases || []).map((db) => [db.name, db]));

  const state = {
    selectedDatabases: new Set(dataset.databaseOrder || []),
    selectedTables: new Set(dataset.tableOrder || []),
  };

  const databaseSelect = initMultiSelect(
    document.getElementById('database-filter'),
    (dataset.databases || []).map((db) => ({
      value: db.name,
      label: createOptionLabel(db.name, db.totals),
    })),
    state.selectedDatabases
  );

  const tableSelect = initMultiSelect(
    document.getElementById('table-filter'),
    (dataset.tables || []).map((table) => ({
      value: table.name,
      label: createOptionLabel(table.name, table.totals),
    })),
    state.selectedTables
  );

  const applyBtn = document.getElementById('apply-filters');
  const summaryEl = document.getElementById('filter-summary');
  const tableContainer = document.querySelector('.table-container');
  const tableElement = document.getElementById('stats-table');
  const tableHead = tableElement.querySelector('thead');
  const tableBody = tableElement.querySelector('tbody');
  const emptyState = document.getElementById('empty-state');

  function initTotals() {
    return { rows: 0, storageBytes: 0, debeziumMinBytes: 0, debeziumMaxBytes: 0 };
  }

  function createOptionLabel(name, totals) {
    const rows = totals && typeof totals.rows === 'number' ? totals.rows : 0;
    const sizeRange = formatRangeForFilter(
      totals ? totals.debeziumMinBytes : null,
      totals ? totals.debeziumMaxBytes : null
    );
    return name + '|' + sizeRange + '|' + numberFormatter.format(rows);
  }

  function formatRangeForFilter(minBytes, maxBytes) {
    if (minBytes == null || maxBytes == null) {
      return '0~0M';
    }
    return formatMegabytes(minBytes, 1) + '~' + formatMegabytes(maxBytes, 1) + 'M';
  }

  function formatMegabytes(bytes, digits) {
    const value = bytes / (1024 * 1024);
    return value.toFixed(digits);
  }

  function formatRangeForCell(minBytes, maxBytes) {
    if (minBytes == null || maxBytes == null) {
      return '-';
    }
    return formatMegabytes(minBytes, 2) + '~' + formatMegabytes(maxBytes, 2) + ' MB';
  }

  function formatStorage(bytes) {
    if (bytes == null) {
      return '-';
    }
    return formatMegabytes(bytes, 2) + ' MB';
  }

  function formatPercent(value) {
    if (value == null || !isFinite(value)) {
      return '-';
    }
    return (value * 100).toFixed(2) + '%';
  }

  function accumulateTotals(target, cell) {
    if (!cell) {
      return;
    }
    if (typeof cell.rows === 'number') {
      target.rows += cell.rows;
    }
    if (typeof cell.storageBytes === 'number') {
      target.storageBytes += cell.storageBytes;
    }
    if (typeof cell.debeziumMinBytes === 'number') {
      target.debeziumMinBytes += cell.debeziumMinBytes;
    }
    if (typeof cell.debeziumMaxBytes === 'number') {
      target.debeziumMaxBytes += cell.debeziumMaxBytes;
    }
  }

  function computeSummary(selectedDbNames, selectedTableNames) {
    const grand = initTotals();
    const perDatabase = {};
    const perTable = {};
    selectedDbNames.forEach((name) => {
      perDatabase[name] = initTotals();
    });
    selectedTableNames.forEach((table) => {
      perTable[table] = initTotals();
    });

    (dataset.databases || []).forEach((db) => {
      if (!perDatabase[db.name]) {
        return;
      }
      const dbTotals = perDatabase[db.name];
      const cellMap = db.tables || {};
      selectedTableNames.forEach((tableName) => {
        const cell = cellMap[tableName];
        if (!cell) {
          return;
        }
        accumulateTotals(dbTotals, cell);
        accumulateTotals(grand, cell);
        accumulateTotals(perTable[tableName], cell);
      });
    });

    return { grand, perDatabase, perTable, selectedDbNames, selectedTableNames };
  }

  function createCellElement(config) {
    const {
      rows,
      share,
      storageBytes,
      debeziumMinBytes,
      debeziumMaxBytes,
      timestampColumn,
      query,
      error,
    } = config;

    const wrapper = document.createElement('div');
    wrapper.className = 'cell-content';

    const copyBtn = document.createElement('button');
    copyBtn.type = 'button';
    copyBtn.className = 'copy-btn';
    copyBtn.title = '复制 SQL';
    copyBtn.innerHTML = '
      <svg viewBox="0 0 16 16" aria-hidden="true"><path d="M3.75 1A1.75 1.75 0 0 0 2 2.75v8.5C2 12.216 2.784 13 3.75 13H5v.75A1.75 1.75 0 0 0 6.75 15h5.5A1.75 1.75 0 0 0 14 13.25v-8.5A1.75 1.75 0 0 0 12.25 3H11V2.75A1.75 1.75 0 0 0 9.25 1zm0 1.5h5.5a.25.25 0 0 1 .25.25V3H6.75A1.75 1.75 0 0 0 5 4.75V12H3.75a.25.25 0 0 1-.25-.25v-8.5a.25.25 0 0 1 .25-.25Zm3 3.25v8.5a.25.25 0 0 0 .25.25h5.5a.25.25 0 0 0 .25-.25v-8.5a.25.25 0 0 0-.25-.25h-5.5a.25.25 0 0 0-.25.25Z"></path></svg>
      <span class="sr-only">复制 SQL</span>';

    const feedback = document.createElement('span');
    feedback.className = 'copy-feedback';
    feedback.textContent = '已复制';

    if (query) {
      copyBtn.addEventListener('click', (event) => {
        event.preventDefault();
        copySql(query, feedback);
      });
    } else {
      copyBtn.disabled = true;
    }

    const lines = document.createElement('div');
    lines.className = 'lines';

    const lineRows = document.createElement('div');
    lineRows.textContent = '行数: ' + (typeof rows === 'number' ? numberFormatter.format(rows) : '-');
    lines.appendChild(lineRows);

    const lineShare = document.createElement('div');
    lineShare.textContent = '占比: ' + formatPercent(share);
    lines.appendChild(lineShare);

    const lineSize = document.createElement('div');
    lineSize.textContent = '大小(网络): ' + formatRangeForCell(debeziumMinBytes, debeziumMaxBytes);
    lines.appendChild(lineSize);

    const storageLine = document.createElement('div');
    storageLine.textContent = '大小(存储): ' + formatStorage(storageBytes);
    lines.appendChild(storageLine);

    if (timestampColumn) {
      const tsLine = document.createElement('div');
      tsLine.className = 'line-secondary';
      tsLine.textContent = '时间列: ' + timestampColumn;
      lines.appendChild(tsLine);
    }

    if (error) {
      const errorLine = document.createElement('div');
      errorLine.className = 'line-error';
      errorLine.textContent = '异常: ' + error;
      lines.appendChild(errorLine);
    }

    wrapper.appendChild(copyBtn);
    wrapper.appendChild(feedback);
    wrapper.appendChild(lines);
    return wrapper;
  }

  function copySql(query, feedbackEl) {
    const showFeedback = () => {
      feedbackEl.classList.add('visible');
      setTimeout(() => feedbackEl.classList.remove('visible'), 1200);
    };
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard.writeText(query).then(showFeedback).catch(() => fallbackCopy(query, feedbackEl, showFeedback));
    } else {
      fallbackCopy(query, feedbackEl, showFeedback);
    }
  }

  function fallbackCopy(query, feedbackEl, onSuccess) {
    const textarea = document.createElement('textarea');
    textarea.value = query;
    textarea.style.position = 'fixed';
    textarea.style.opacity = '0';
    document.body.appendChild(textarea);
    textarea.select();
    try {
      const successful = document.execCommand('copy');
      if (successful) {
        onSuccess();
      } else {
        alert('无法复制，请手动复制 SQL:
' + query);
      }
    } catch (err) {
      alert('无法复制，请手动复制 SQL:
' + query);
    } finally {
      document.body.removeChild(textarea);
    }
  }

  function renderTable(selectedDatabaseSet, selectedTableSet) {
    const selectedDbNames = Array.from(selectedDatabaseSet);
    const selectedTableNames = Array.from(selectedTableSet);

    tableHead.innerHTML = '';
    tableBody.innerHTML = '';

    if (selectedDbNames.length === 0 || selectedTableNames.length === 0) {
      tableContainer.style.display = 'none';
      emptyState.hidden = false;
      const emptySummary = { grand: initTotals(), perDatabase: {}, perTable: {} };
      updateSummary(emptySummary);
      return emptySummary;
    }

    tableContainer.style.display = 'block';
    emptyState.hidden = true;

    const summary = computeSummary(selectedDbNames, selectedTableNames);

    const headerRow = document.createElement('tr');
    const dbHeader = document.createElement('th');
    dbHeader.textContent = '数据库';
    headerRow.appendChild(dbHeader);
    const totalHeader = document.createElement('th');
    totalHeader.textContent = '总计';
    headerRow.appendChild(totalHeader);
    selectedTableNames.forEach((tableName) => {
      const th = document.createElement('th');
      th.textContent = tableName;
      headerRow.appendChild(th);
    });
    tableHead.appendChild(headerRow);

    const totalsRow = document.createElement('tr');
    totalsRow.className = 'totals-row';
    const totalLabel = document.createElement('th');
    totalLabel.textContent = '总计';
    totalsRow.appendChild(totalLabel);

    const aggregatedQueryHint = '-- 汇总: 请针对各数据库、各表单独执行 SQL 进行核对。';

    const totalCell = document.createElement('td');
    totalCell.appendChild(
      createCellElement({
        rows: summary.grand.rows,
        share: summary.grand.rows > 0 ? 1 : null,
        storageBytes: summary.grand.storageBytes,
        debeziumMinBytes: summary.grand.debeziumMinBytes,
        debeziumMaxBytes: summary.grand.debeziumMaxBytes,
        timestampColumn: null,
        query: aggregatedQueryHint,
        error: null,
      })
    );
    totalsRow.appendChild(totalCell);

    selectedTableNames.forEach((tableName) => {
      const tableTotals = summary.perTable[tableName] || initTotals();
      const cell = document.createElement('td');
      const share = summary.grand.rows > 0 && tableTotals.rows > 0
        ? tableTotals.rows / summary.grand.rows
        : (tableTotals.rows === 0 ? 0 : null);
      cell.appendChild(
        createCellElement({
          rows: tableTotals.rows,
          share,
          storageBytes: tableTotals.storageBytes,
          debeziumMinBytes: tableTotals.debeziumMinBytes,
          debeziumMaxBytes: tableTotals.debeziumMaxBytes,
          timestampColumn: null,
          query: aggregatedQueryHint,
          error: null,
        })
      );
      totalsRow.appendChild(cell);
    });
    tableBody.appendChild(totalsRow);

    selectedDbNames.forEach((dbName) => {
      const db = databaseMap.get(dbName);
      const dbRow = document.createElement('tr');
      const dbHeaderCell = document.createElement('th');
      dbHeaderCell.textContent = dbName;
      dbRow.appendChild(dbHeaderCell);

      const dbTotals = summary.perDatabase[dbName] || initTotals();
      const dbTotalCell = document.createElement('td');
      const dbShare = summary.grand.rows > 0 && dbTotals.rows > 0
        ? dbTotals.rows / summary.grand.rows
        : (dbTotals.rows === 0 ? 0 : null);
      dbTotalCell.appendChild(
        createCellElement({
          rows: dbTotals.rows,
          share: dbShare,
          storageBytes: dbTotals.storageBytes,
          debeziumMinBytes: dbTotals.debeziumMinBytes,
          debeziumMaxBytes: dbTotals.debeziumMaxBytes,
          timestampColumn: null,
          query: '-- 汇总: 数据库 ' + dbName + ' 聚合统计，请逐表执行 SQL。',
          error: null,
        })
      );
      dbRow.appendChild(dbTotalCell);

      selectedTableNames.forEach((tableName) => {
        const cellData = db && db.tables ? db.tables[tableName] : null;
        const cell = document.createElement('td');
        if (!cellData) {
          cell.appendChild(
            createCellElement({
              rows: null,
              share: null,
              storageBytes: null,
              debeziumMinBytes: null,
              debeziumMaxBytes: null,
              timestampColumn: null,
              query: '-- 表 ' + tableName + ' 在数据库 ' + dbName + ' 中不存在或无 creat* 字段。',
              error: null,
            })
          );
        } else {
          const shareBase = dbTotals.rows;
          const shareValue = shareBase > 0 && typeof cellData.rows === 'number'
            ? cellData.rows / shareBase
            : (cellData.rows === 0 ? 0 : null);
          cell.appendChild(
            createCellElement({
              rows: cellData.rows,
              share: shareValue,
              storageBytes: cellData.storageBytes,
              debeziumMinBytes: cellData.debeziumMinBytes,
              debeziumMaxBytes: cellData.debeziumMaxBytes,
              timestampColumn: cellData.timestampColumn,
              query: cellData.query,
              error: cellData.error,
            })
          );
        }
        dbRow.appendChild(cell);
      });

      tableBody.appendChild(dbRow);
    });

    updateSummary(summary);
    return summary;
  }

  function updateSummary(summary) {
    if (!summaryEl) {
      return;
    }
    const rows = summary && summary.grand ? summary.grand.rows : 0;
    const minBytes = summary && summary.grand ? summary.grand.debeziumMinBytes : 0;
    const maxBytes = summary && summary.grand ? summary.grand.debeziumMaxBytes : 0;
    const storageBytes = summary && summary.grand ? summary.grand.storageBytes : 0;
    summaryEl.textContent =
      '行数: ' + numberFormatter.format(rows) +
      ' | 大小(网络): ' + formatRangeForCell(minBytes, maxBytes) +
      ' | 大小(存储): ' + formatStorage(storageBytes);
  }

  function initMultiSelect(container, options, defaultSelected) {
    const selected = new Set(defaultSelected || []);
    const toggle = container.querySelector('.multi-select-toggle');
    const optionsHost = container.querySelector('.multi-select-options');
    const searchInput = container.querySelector('.multi-select-search');
    const selectAllBtn = container.querySelector('[data-action="select-all"]');
    const clearAllBtn = container.querySelector('[data-action="clear-all"]');
    const title = container.dataset.title || '';

    function updateToggleText() {
      toggle.textContent = `${title}（已选 ${selected.size}/${options.length}）`;
    }

    function setOpen(open) {
      container.classList.toggle('open', open);
    }

    toggle.addEventListener('click', (event) => {
      event.preventDefault();
      const willOpen = !container.classList.contains('open');
      document.querySelectorAll('.multi-select.open').forEach((el) => {
        if (el !== container) {
          el.classList.remove('open');
        }
      });
      setOpen(willOpen);
    });

    document.addEventListener('click', (event) => {
      if (!container.contains(event.target)) {
        setOpen(false);
      }
    });

    optionsHost.innerHTML = '';
    options.forEach((option) => {
      const label = document.createElement('label');
      label.className = 'multi-select-option';
      const checkbox = document.createElement('input');
      checkbox.type = 'checkbox';
      checkbox.value = option.value;
      checkbox.checked = selected.has(option.value);
      const span = document.createElement('span');
      span.textContent = option.label;
      label.appendChild(checkbox);
      label.appendChild(span);
      optionsHost.appendChild(label);

      checkbox.addEventListener('change', () => {
        if (checkbox.checked) {
          selected.add(option.value);
        } else {
          selected.delete(option.value);
        }
        updateToggleText();
      });
    });

    updateToggleText();

    if (selectAllBtn) {
      selectAllBtn.addEventListener('click', (event) => {
        event.preventDefault();
        optionsHost.querySelectorAll('input[type="checkbox"]').forEach((checkbox) => {
          checkbox.checked = true;
          selected.add(checkbox.value);
        });
        updateToggleText();
      });
    }

    if (clearAllBtn) {
      clearAllBtn.addEventListener('click', (event) => {
        event.preventDefault();
        optionsHost.querySelectorAll('input[type="checkbox"]').forEach((checkbox) => {
          checkbox.checked = false;
          selected.delete(checkbox.value);
        });
        updateToggleText();
      });
    }

    if (searchInput) {
      searchInput.addEventListener('input', () => {
        const keyword = searchInput.value.trim().toLowerCase();
        optionsHost.querySelectorAll('.multi-select-option').forEach((optionEl) => {
          const text = optionEl.textContent.toLowerCase();
          optionEl.style.display = text.includes(keyword) ? '' : 'none';
        });
      });
    }

    return {
      getSelected() {
        return new Set(selected);
      },
    };
  }

  applyBtn.addEventListener('click', () => {
    state.selectedDatabases = databaseSelect.getSelected();
    state.selectedTables = tableSelect.getSelected();
    const summary = renderTable(state.selectedDatabases, state.selectedTables);
    updateSummary(summary);
  });

  const initialSummary = renderTable(state.selectedDatabases, state.selectedTables);
  updateSummary(initialSummary);
})();
  </script>
</body>
</html>
