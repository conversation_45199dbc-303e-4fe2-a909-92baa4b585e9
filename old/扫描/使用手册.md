# old/扫描 使用手册

## 目录目的
`old/扫描` 存放 MySQL CDC 容量评估工具及其输出，主要用于估算近 N 小时内 `apple_server_notifications` 相关表的变更规模、识别潜在的 Debezium 压力点，并为 CDC 配额决策提供数据支撑。

## 主要文件
- `mysql_scan.py`：核心扫描脚本，通过 SQL 统计行数、估算 Debezium 负载并生成报表。
- `combined_dataset.json`：扫描结果的缓存与合并文件，可在多次扫描后沉淀完整数据。
- `index.html`：交互式报表，总览 48 小时窗口内各库各表的 CDC 估算值。
- `index_camera.html`：静态快照版报表，适合截屏或离线分享。
- `mysql_scan.log`：最近一次扫描的逐表执行日志，便于排查超时或失败。
- `skipped_tables.md`：记录因超时等原因跳过的库表列表。
- `second_number_2_creat_columns.md`：历史统计的 `creat*` 字段补全情况，可辅助确认时间窗字段。

## 环境准备
- Python ≥ 3.9（脚本使用 `dataclasses`、`Path` 等标准库特性）。
- 依赖：`pip install pymysql`。
- 数据库权限：至少需要对目标 schema 拥有 `SELECT` 权限，以及对 `information_schema` 的查询权限。
- 网络：保证到目标 MySQL 的出站访问；长时间扫描建议使用稳定网络环境。

## 快速上手
1. **激活虚拟环境（可选）**
   ```bash
   python3 -m venv .venv
   source .venv/bin/activate
   pip install pymysql
   ```
2. **执行扫描**
   ```bash
   python old/扫描/mysql_scan.py \
     --dsn "user:password@tcp(host:port)" \
     --hours 48 \
     --html old/扫描/index.html \
     --json old/扫描/combined_dataset.json
   ```
   - `--dsn`：覆盖脚本内置的只读 DSN，建议显式传入防止凭据变更导致失败。
   - `--hours`：窗口大小，默认 48，可根据业务高峰调整。
   - `--html`：生成报表的输出位置，若省略则跳过 HTML 渲染。
   - `--json`：指定缓存文件，脚本会将本次结果与历史结果合并后写回。
3. **仅重渲染报表**
   若已拥有缓存结果，可跳过数据库扫描：
   ```bash
   python old/扫描/mysql_scan.py \
     --json old/扫描/combined_dataset.json \
     --html old/扫描/index.html \
     --render-only
   ```

## 扫描流程
- 连接目标 MySQL，收集包含 `apple_server_notifications` 的库表及关联表。
- 优先选择包含 `creat*` 前缀的时间列，估算滚动窗口的增量；若缺失则退回全量统计。
- 逐表执行行数、平均行长、存储体积估算，并推导 Debezium 最小/最大负载区间。
- 将原始统计写入 `combined_dataset.json`，并调用模板渲染 `index*.html`。
- 执行详情与超时信息同步写入 `mysql_scan.log` 和 `skipped_tables.md`。

## 输出解读
- `index.html`
  - 顶部筛选支持按库、表、标签快速聚焦数据。
  - 每个单元格展示行数、占比、估算带宽/存储；红色高亮提示接近或超出阈值。
- `index_camera.html`
  - 与主报表结构一致，但移除了交互逻辑，适用于只读分享。
- `combined_dataset.json`
  - `databases` → 多库数据集；`totals` → 汇总指标；可作为二次分析输入。
- `mysql_scan.log`
  - `[x/y] schema.table - status` 表示常规进度；`[SKIP]` / `COUNT 超时` 需关注并视情况补跑。
- `skipped_tables.md`
  - Markdown 表格列出被跳过库表与原因，可整理给 DBA 进一步分析。

## 占比计算规则
- 单元格占比 = 当前库表在所有库中对应表的行数 / 该表的全库总行数。
- 页面顶部“窗口起始/生成时间”来源于扫描时的 UTC 时间戳。
- Debezium 负载估算：行数 × (平均行长 + 320 Bytes)；若行长缺失则使用 128 Bytes 下限。

## 常用排障提示
- **依赖缺失**：启动时报 `Missing dependency: pymysql`，重新执行 `pip install pymysql`。
- **数据库连接失败**：确认 `--dsn`、安全组、白名单；支持 `host:port` 或 `host` 默认 3306。
- **长表超时**：`MAX_EXECUTION_TIME_MS` 固定 45s，可在脚本内调整或分库重跑。
- **缓存冲突**：若 `combined_dataset.json` 结构损坏，删除后重跑一次完整扫描。

## 维护建议
- 将扫描命令封装为 `Makefile` 或 CI 定时任务，定期刷新数据。
- 生成报表后将 `index_camera.html` 存档，以便回溯不同时间点的容量状况。
- 若新增 CDC 表或字段，需同步更新 `mysql_scan.py` 中的白名单与 `second_number_2_creat_columns.md`。
- 建议在提交前验证 `python old/扫描/mysql_scan.py --help` 是否按预期展示所有参数说明。
