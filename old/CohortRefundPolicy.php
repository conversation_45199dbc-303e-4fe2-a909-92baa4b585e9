<?php

namespace App\Models\Cohort;

use App\Models\BaseModel;
use Carbon\Carbon;

/**
 * 退款策略模型
 * 
 * 表名：refund_policies
 * 功能：定义和管理向苹果回传消费信息的策略配置
 * 
 * 主要字段说明：
 * - id: 策略ID
 * - name: 策略名称
 * - description: 策略说明
 * - parse_mode: 解析模式（hardcoded=硬编码, rp_control=RP比例控制）
 * - rp_1_percent: refundPreference=1的目标比例（仅rp_control模式）
 * - rp_2_percent: refundPreference=2的目标比例（仅rp_control模式）
 * - rp_3_percent: refundPreference=3的目标比例（仅rp_control模式）
 * - rp_1_params: refundPreference=1时的固定参数值（仅rp_control模式）
 * - rp_2_params: refundPreference=2时的固定参数值（仅rp_control模式）
 * - rp_3_params: refundPreference=3时的固定参数值（仅rp_control模式）
 * - remarks: 备注
 * 
 * 策略模式说明：
 * 1. hardcoded模式：使用预定义的策略逻辑
 *    - 策略1：基础策略，传递用户真实使用数据，refundPreference=2
 *    - 策略2：策略1基础上，playTime=0（未声明使用时长）
 *    - 策略3：策略2基础上，consumptionStatus=3（商品已完全消费）
 *    - 策略4：策略1基础上，refundPreference=3（无偏好）
 *    - 策略5：策略4基础上，playTime=0
 *    - 策略6：根据使用时长动态调整refundPreference
 * 
 * 2. rp_control模式：按配置比例随机分配refundPreference值
 *    - 先调用policy0计算真实消费数据
 *    - 按rp_1_percent、rp_2_percent、rp_3_percent的比例随机选择RP值
 *    - 使用对应的rp_X_params覆盖部分参数
 * 
 * refundPreference值含义：
 * - 1: 希望苹果批准退款
 * - 2: 希望苹果拒绝退款
 * - 3: 无偏好，由苹果决定
 * 
 * @property int|null $id 主键ID
 * @property string|null $name 退款回传策略名称
 * @property string|null $description 传值说明
 * @property string $parse_mode 策略解析模式
 * @property int|null $rp_1_percent refundPreference=1的目标回传比例
 * @property int|null $rp_2_percent refundPreference=2的目标回传比例
 * @property int|null $rp_3_percent refundPreference=3的目标回传比例
 * @property array|null $rp_1_params refundPreference=1时的固定参数值JSON
 * @property array|null $rp_2_params refundPreference=2时的固定参数值JSON
 * @property array|null $rp_3_params refundPreference=3时的固定参数值JSON
 * @property string|null $remarks 备注
 * @property string|null $created_at 创建时间
 * @property string|null $updated_at 更新时间
 */
class CohortRefundPolicy extends BaseModel
{
    protected $connection = 'cohort';

    protected $table = 'refund_policies';

    protected $guarded = [];

    // 新增字段定义
    protected $casts = [
        'rp_1_params' => 'array',
        'rp_2_params' => 'array',
        'rp_3_params' => 'array',
    ];

    /**
     * 计算退款请求的消费信息
     * 
     * 根据指定的策略ID计算要向苹果回传的消费信息参数。
     * 这些参数会影响苹果对退款请求的审批决策。
     * 
     * @param int $adamId App的Adam ID
     * @param string $transactionId 交易ID
     * @param string $appAccountToken App账户令牌
     * @param int $policyId 退款策略ID（默认为1）
     * @return array 包含orderInfo和consumptionInfo的数组
     * @throws \Exception 当策略不存在或配置错误时
     * 
     * @see https://developer.apple.com/documentation/appstoreserverapi/consumptionrequest
     */
    public static function computeRefundInfo($adamId, $transactionId, $appAccountToken, $policyId = 1)
    {
        $order = CohortOrder::forApp($adamId)
            ->where('transaction_id', $transactionId)
            ->first();
        if (empty($order)) {
            $orderInfo = [
                'regionId' => null,
                'transactionId' => $transactionId,
                'skuDisplayName' => null,
                'skuPrice' => null,
                'orderTime' => null,
                'refundSuccessTime' => null,
            ];
            $consumptionInfo = [
                'accountTenure' => 0,
                'consumptionStatus' => 0,
                'customerConsented' => true,
                'deliveryStatus' => 0,
                'lifetimeDollarsPurchased' => 0,
                'lifetimeDollarsRefunded' => 0,
                'platform' => 1,
                'playTime' => 0,
                'refundPreference' => 2,
                'sampleContentProvided' => false,
                'userStatus' => 0,
                'downloadTime' => null,
                'diffInMinutes' => null,
                'diffInHours' => null,
                'diffInDays' => null,
                'appAccountToken' => $appAccountToken,
            ];
            $result = [
                'orderInfo' => $orderInfo,
                'consumptionInfo' => $consumptionInfo,
            ];
            return $result;
        }
        $orderInfo =  [
            'regionId' => $order->region_id,
            'transactionId' => $transactionId,
            'skuDisplayName' => $order->sku_display_name,
            'skuPrice' => $order->price,
            'orderTime' => $order->order_time,
            'refundSuccessTime' => $order->refund_time,
        ];

        // 获取策略配置
        $policy = self::find($policyId);
        if (!$policy) {
            throw new \Exception("Refund policy #$policyId not found");
        }

        // 根据策略模式选择处理方式
        if ($policy->parse_mode === 'rp_control') {
            $consumptionInfo = self::processRpControlMode($policy, $adamId, $transactionId);
        } else {
            // 硬编码模式，保持原有逻辑
            $consumptionInfo = self::processHardcodedMode($policyId, $adamId, $transactionId);
        }

        $consumptionInfo['appAccountToken'] = $appAccountToken;
        $result = [
            'policyId' => $policyId,
            'orderInfo' => $orderInfo,
            'consumptionInfo' => $consumptionInfo,
        ];
        return $result;
    }

    /**
     * 处理硬编码模式
     */
    private static function processHardcodedMode($policyId, $adamId, $transactionId)
    {
        if ($policyId === 1) {
            return self::policy1($adamId, $transactionId);
        } elseif ($policyId === 2) {
            $consumptionInfo = self::policy1($adamId, $transactionId);
            $consumptionInfo['playTime'] = 0; // The engagement time is undeclared
            return $consumptionInfo;
        } elseif ($policyId === 3) {
            $consumptionInfo = self::policy1($adamId, $transactionId);
            $consumptionInfo['playTime'] = 0; // The engagement time is undeclared
            $consumptionInfo['consumptionStatus'] = 3; // The in-app purchase is fully consumed
            return $consumptionInfo;
        } elseif ($policyId === 4) {
            $consumptionInfo = self::policy1($adamId, $transactionId);
            $consumptionInfo['refundPreference'] = 3; // You have no preference whether Apple grants or declines the refund
            return $consumptionInfo;
        } elseif ($policyId === 5) {
            $consumptionInfo = self::policy1($adamId, $transactionId);
            $consumptionInfo['refundPreference'] = 3; // You have no preference whether Apple grants or declines the refund
            $consumptionInfo['playTime'] = 0; // The engagement time is undeclared
            return $consumptionInfo;
        } elseif ($policyId === 6) {
            return self::policy6($adamId, $transactionId);
        } else {
            throw new \Exception("Unknown refund policy #$policyId");
        }
    }

    /**
     * 处理RP比例控制模式
     */
    private static function processRpControlMode($policy, $adamId, $transactionId)
    {
        // 验证比例配置
        $totalPercent = $policy->rp_1_percent + $policy->rp_2_percent + $policy->rp_3_percent;
        if ($totalPercent !== 100) {
            throw new \Exception("RP percentages must sum to 100, got $totalPercent for policy #{$policy->id}");
        }

        // 先调用policy0计算如实传值的结果
        $consumptionInfo = self::policy0($adamId, $transactionId);

        // 按概率选择refundPreference值
        $selectedRp = self::selectRefundPreferenceByProbability(
            $policy->rp_1_percent,
            $policy->rp_2_percent,
            $policy->rp_3_percent
        );

        // 根据选择的RP值覆盖参数
        $paramsField = "rp_{$selectedRp}_params";
        $overrideParams = $policy->$paramsField;

        if ($overrideParams && is_array($overrideParams)) {
            foreach ($overrideParams as $key => $value) {
                $consumptionInfo[$key] = $value;
            }
        }

        // 确保refundPreference被设置为选中的值
        $consumptionInfo['refundPreference'] = $selectedRp;

        return $consumptionInfo;
    }

    /**
     * 按概率选择refundPreference值
     */
    private static function selectRefundPreferenceByProbability($rp1Percent, $rp2Percent, $rp3Percent)
    {
        $random = mt_rand(1, 100);
        // 简单的累积概率判断
        if ($random <= $rp1Percent) {
            return 1;
        } elseif ($random <= $rp1Percent + $rp2Percent) {
            return 2;
        } else {
            return 3;
        }
    }

    /**
     * 退款策略 0: 如实传值，不作干预，无退款偏好。
     */
    private static function policy0($adamId, $transactionId)
    {
        $order = CohortOrder::forApp($adamId)
            ->where('transaction_id', $transactionId)
            ->first();
        $downloadTime = CohortOrder::forApp($adamId)
            ->where('first_original_transaction_id', $order->first_original_transaction_id)
            ->min('download_time');
        $hasTrialOrder = CohortOrder::forApp($adamId)
            ->where('first_original_transaction_id', $order->first_original_transaction_id)
            ->where('is_trial', 1)
            ->exists();
        $now = now();
        $refundPreference = 3;
        if (empty($downloadTime)) {
            $diffInMinutes = null;
            $diffInHours = null;
            $diffInDays = null;
            $accountTenure = 0;
            $deliveryStatus = 5;
            $consumptionStatus = 0;
            $playTime = 0;
            $userStatus = 0;
            $lifetimeDollarsPurchased = 0;
            $lifetimeDollarsRefunded = 0;
        } else {
            $downloadTimeCarbon = new Carbon($downloadTime, 'UTC');
            $diffInMinutes = $downloadTimeCarbon->diffInMinutes($now);
            $diffInHours = $downloadTimeCarbon->diffInHours($now);
            $diffInDays = $downloadTimeCarbon->diffInDays($now);
            if ($diffInDays < 3) {
                $accountTenure = 1;
            } elseif ($diffInDays < 10) {
                $accountTenure = 2;
            } elseif ($diffInDays < 30) {
                $accountTenure = 3;
            } elseif ($diffInDays < 90) {
                $accountTenure = 4;
            } elseif ($diffInDays < 180) {
                $accountTenure = 5;
            } elseif ($diffInDays < 365) {
                $accountTenure = 6;
            } else {
                $accountTenure = 7;
            }
            if ($diffInMinutes < 5) {
                $playTime = 1;
            } elseif ($diffInMinutes < 60) {
                $playTime = 2;
            } elseif ($diffInHours < 6) {
                $playTime = 3;
            } elseif ($diffInHours < 24) {
                $playTime = 4;
            } elseif ($diffInDays < 4) {
                $playTime = 5;
            } elseif ($diffInDays < 16) {
                $playTime = 6;
            } else {
                $playTime = 7;
            }
            $deliveryStatus = 0;
            $consumptionStatus = 3;
            $userStatus = 1;
            $lifetimeDollarsPurchasedAmount = CohortOrder::forApp($adamId)
                ->where('first_original_transaction_id', $order->first_original_transaction_id)
                ->sum('sales');
            $lifetimeDollarsPurchased = self::mapLifetimeDollars($lifetimeDollarsPurchasedAmount);
            $lifetimeDollarsRefundedAmount = abs(CohortOrder::forApp($adamId)
                ->where('first_original_transaction_id', $order->first_original_transaction_id)
                ->sum('refunds'));
            $lifetimeDollarsRefunded = self::mapLifetimeDollars($lifetimeDollarsRefundedAmount);
        }
        $consumptionInfo = [
            'accountTenure' => $accountTenure,
            'consumptionStatus' => $consumptionStatus,
            'customerConsented' => true,
            'deliveryStatus' => $deliveryStatus,
            'lifetimeDollarsPurchased' => $lifetimeDollarsPurchased,
            'lifetimeDollarsRefunded' => $lifetimeDollarsRefunded,
            'platform' => 1,
            'playTime' => $playTime,
            'refundPreference' => $refundPreference,
            'sampleContentProvided' => $hasTrialOrder,
            'userStatus' => $userStatus,
            'downloadTime' => $downloadTime,
            'diffInMinutes' => $diffInMinutes,
            'diffInHours' => $diffInHours,
            'diffInDays' => $diffInDays,
        ];
        return $consumptionInfo;
    }

    private static function policy1($adamId, $transactionId)
    {
        $order = CohortOrder::forApp($adamId)
            ->where('transaction_id', $transactionId)
            ->first();
        $downloadTime = CohortOrder::forApp($adamId)
            ->where('first_original_transaction_id', $order->first_original_transaction_id)
            ->min('download_time');
        $hasTrialOrder = CohortOrder::forApp($adamId)
            ->where('first_original_transaction_id', $order->first_original_transaction_id)
            ->where('is_trial', 1)
            ->exists();
        $now = now();
        if (empty($downloadTime)) {
            $accountTenure = 0;
            $playTime = 0;
            $diffInMinutes = null;
            $diffInHours = null;
            $diffInDays = null;
        } else {
            $downloadTimeCarbon = new Carbon($downloadTime, 'UTC');
            $diffInMinutes = $downloadTimeCarbon->diffInMinutes($now);
            $diffInHours = $downloadTimeCarbon->diffInHours($now);
            $diffInDays = $downloadTimeCarbon->diffInDays($now);
            if ($diffInMinutes < 5) {
                $playTime = 1;
            } elseif ($diffInMinutes < 60) {
                $playTime = 2;
            } elseif ($diffInHours < 6) {
                $playTime = 3;
            } elseif ($diffInHours < 24) {
                $playTime = 4;
            } elseif ($diffInDays < 4) {
                $playTime = 5;
            } elseif ($diffInDays < 16) {
                $playTime = 6;
            } else {
                $playTime = 7;
            }
            $playTime = max( 4, $playTime);
            if ($diffInDays < 3) {
                $accountTenure = 1;
            } elseif ($diffInDays < 10) {
                $accountTenure = 2;
            } elseif ($diffInDays < 30) {
                $accountTenure = 3;
            } elseif ($diffInDays < 90) {
                $accountTenure = 4;
            } elseif ($diffInDays < 180) {
                $accountTenure = 5;
            } elseif ($diffInDays < 365) {
                $accountTenure = 6;
            } else {
                $accountTenure = 7;
            }
        }
        $consumptionInfo = [
            'accountTenure' => $accountTenure,
            'consumptionStatus' => 0,
            'customerConsented' => true,
            'deliveryStatus' => 0,
            'lifetimeDollarsPurchased' => 0,
            'lifetimeDollarsRefunded' => 0,
            'platform' => 1,
            'playTime' => $playTime,
            'refundPreference' => 2,
            'sampleContentProvided' => $hasTrialOrder,
            'userStatus' => 1,
            'downloadTime' => $downloadTime,
            'diffInMinutes' => $diffInMinutes,
            'diffInHours' => $diffInHours,
            'diffInDays' => $diffInDays,
        ];
        return $consumptionInfo;
    }

    private static function policy6($adamId, $transactionId)
    {
        $order = CohortOrder::forApp($adamId)
            ->where('transaction_id', $transactionId)
            ->first();
        $downloadTime = CohortOrder::forApp($adamId)
            ->where('first_original_transaction_id', $order->first_original_transaction_id)
            ->min('download_time');
        $hasTrialOrder = CohortOrder::forApp($adamId)
            ->where('first_original_transaction_id', $order->first_original_transaction_id)
            ->where('is_trial', 1)
            ->exists();
        $now = now();
        if (empty($downloadTime)) {
            $diffInMinutes = null;
            $diffInHours = null;
            $diffInDays = null;
            $accountTenure = 0;
            $deliveryStatus = 5;
            $consumptionStatus = 0;
            $playTime = 0;
            $refundPreference = 3;
            $userStatus = 0;
            $lifetimeDollarsPurchased = 0;
            $lifetimeDollarsRefunded = 0;
        } else {
            $downloadTimeCarbon = new Carbon($downloadTime, 'UTC');
            $diffInMinutes = $downloadTimeCarbon->diffInMinutes($now);
            $diffInHours = $downloadTimeCarbon->diffInHours($now);
            $diffInDays = $downloadTimeCarbon->diffInDays($now);
            if ($diffInDays < 3) {
                $accountTenure = 1;
            } elseif ($diffInDays < 10) {
                $accountTenure = 2;
            } elseif ($diffInDays < 30) {
                $accountTenure = 3;
            } elseif ($diffInDays < 90) {
                $accountTenure = 4;
            } elseif ($diffInDays < 180) {
                $accountTenure = 5;
            } elseif ($diffInDays < 365) {
                $accountTenure = 6;
            } else {
                $accountTenure = 7;
            }
            if ($diffInMinutes < 5) {
                $playTime = 1;
            } elseif ($diffInMinutes < 60) {
                $playTime = 2;
            } elseif ($diffInHours < 6) {
                $playTime = 3;
            } elseif ($diffInHours < 24) {
                $playTime = 4;
            } elseif ($diffInDays < 4) {
                $playTime = 5;
            } elseif ($diffInDays < 16) {
                $playTime = 6;
            } else {
                $playTime = 7;
            }
            if ($playTime === 1 || $playTime === 2) {
                $refundPreference = 1;
            } elseif ($playTime === 3) {
                $refundPreference = 3;
            } else {
                $refundPreference = 2;
            }
            $deliveryStatus = 0;
            $consumptionStatus = 3;
            $userStatus = 1;
            $lifetimeDollarsPurchasedAmount = CohortOrder::forApp($adamId)
                ->where('first_original_transaction_id', $order->first_original_transaction_id)
                ->sum('sales');
            $lifetimeDollarsPurchased = self::mapLifetimeDollars($lifetimeDollarsPurchasedAmount);
            $lifetimeDollarsRefundedAmount = abs(CohortOrder::forApp($adamId)
                ->where('first_original_transaction_id', $order->first_original_transaction_id)
                ->sum('refunds'));
            $lifetimeDollarsRefunded = self::mapLifetimeDollars($lifetimeDollarsRefundedAmount);
        }
        $consumptionInfo = [
            'accountTenure' => $accountTenure,
            'consumptionStatus' => $consumptionStatus,
            'customerConsented' => true,
            'deliveryStatus' => $deliveryStatus,
            'lifetimeDollarsPurchased' => $lifetimeDollarsPurchased,
            'lifetimeDollarsRefunded' => $lifetimeDollarsRefunded,
            'platform' => 1,
            'playTime' => $playTime,
            'refundPreference' => $refundPreference,
            'sampleContentProvided' => $hasTrialOrder,
            'userStatus' => $userStatus,
            'downloadTime' => $downloadTime,
            'diffInMinutes' => $diffInMinutes,
            'diffInHours' => $diffInHours,
            'diffInDays' => $diffInDays,
        ];
        return $consumptionInfo;
    }

    private static function mapLifetimeDollars($amount)
    {
        if ($amount === null) {
            return 0; // 未声明
        }
        if ($amount <= 0) {
            return 1; // 0 USD
        }
        if ($amount < 50) {
            return 2; // 0.01-49.99 USD
        }
        if ($amount < 100) {
            return 3; // 50-99.99 USD
        }
        if ($amount < 500) {
            return 4; // 100-499.99 USD
        }
        if ($amount < 1000) {
            return 5; // 500-999.99 USD
        }
        if ($amount < 2000) {
            return 6; // 1000-1999.99 USD
        }
        return 7; // 2000+ USD
    }
}
