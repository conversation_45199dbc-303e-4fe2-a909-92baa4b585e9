<?php

namespace App\Models\Cohort;

use App\Models\BaseModel;

/**
 * 退款请求记录模型
 * 
 * 表名：refund_requests
 * 功能：记录和跟踪苹果退款请求的完整生命周期
 * 
 * 主要字段说明：
 * - 基础信息：app_id, adam_id, transaction_id, account_id
 * - 时间跟踪：first_push_time(首次推送), process_time(处理时间), refund_success_time(退款成功时间)
 * - 处理状态：process_result(处理结果), refund_result(最终退款结果)
 * - 消费信息：account_tenure, play_time, consumption_status, refund_preference等
 * 
 * 索引设计（优化后）：
 * 1. PRIMARY KEY (id) - 主键索引
 * 2. uni_original_record (app_id, original_record_id) - 防止重复记录
 * 3. idx_process_pending (process_time, sched_process_time, adam_id) - 查找待处理请求
 * 4. idx_track_v2 (refund_result, first_push_time, refund_result_track_time, id) - 跟踪退款结果，支持分片
 * 5. idx_transaction (adam_id, transaction_id) - 按交易ID查询
 * 6. idx_stats (the_day, refund_result, process_result) - 统计分析
 * 
 * 业务流程：
 * 1. CohortSyncRefundRequests - 从各App数据库同步退款请求
 * 2. CohortProcessRefundRequests - 处理请求并向苹果回传消费信息
 * 3. CohortTrackProcessedRefundRequests - 跟踪最终退款结果（支持10个worker分片）
 * 
 * @property int $id 主键ID
 * @property int|null $app_id App ID
 * @property int|null $developer_id 开发者ID
 * @property int|null $adam_id Adam ID
 * @property string|null $app_name App名称
 * @property string|null $transaction_id 初次购买ID
 * @property int|null $account_id 用户ID
 * @property string|null $first_push_time 首次推送通知时间
 * @property int|null $push_cnt 累计推送通知的次数
 * @property string|null $oa_sync_time OA拉取时间
 * @property string|null $region_id 地区ID
 * @property string|null $sku_display_name SKU显示名称
 * @property string|null $refund_reason 退款原因
 * @property int|null $refund_policy_id 退款政策ID
 * @property int|null $sched_process_delay_hours 计划的延迟处理小时数
 * @property string|null $sched_process_time 计划处理时间
 * @property string|null $process_time 处理时间
 * @property float|null $process_delay_hours 距离首次推送的处理延迟
 * @property string|null $process_result 处理结果（回传成功/回传失败/超时跳过/Exception）
 * @property float|null $refund_result_track_hours 最终退款结果已经跟踪了多少小时
 * @property float|null $refund_success_hours 退款成功花了多少小时
 * @property string|null $refund_result 最终退款结果（已退款(订单轮询)/已退款(收到退款通知)/超过X天未退款/超过X天未退款(收到拒绝通知)）
 * @property string|null $refund_result_track_time 最终退款结果最近一次跟踪时间
 * @property string|null $refund_success_time 退款成功时间
 * @property string|null $first_refund_decline_time 首次REFUND_DECLINED通知时间
 * @property string|null $last_refund_decline_time 最后一次REFUND_DECLINED通知时间
 * @property int|null $process_response_code 苹果返回的状态码
 * @property float|null $download_to_refund_request_hours 从下载到发起退款请求的小时数
 * @property float|null $order_to_refund_request_hours 从购买到发起退款请求的小时数
 * @property string|null $download_time 首次下载时间
 * @property string|null $order_time 购买时间
 * @property int|null $account_tenure 账号年龄
 * @property int|null $play_time 用户使用时长
 * @property int|null $consumption_status 内购商品是否已使用
 * @property string|null $sample_content_provided 是否免费试用过
 * @property int|null $lifetime_dollars_purchased 用户累计购买金额
 * @property int|null $lifetime_dollars_refunded 用户累计退款金额
 * @property int|null $refund_preference 开发者是否允许用户退款
 * @property int|null $download_to_process_hours 处理请求时距离首次下载小时数
 * @property int|null $download_to_process_minutes 处理请求时距离首次下载分钟数
 * @property float|null $sku_price SKU价格数值
 * @property string|null $customer_consented 用户是否同意
 * @property int|null $delivery_status 商品是否交付
 * @property int|null $platform 平台
 * @property int|null $user_status 用户账号是否有效
 * @property string|null $process_response_body 苹果返回的正文或错误消息
 * @property int|null $download_to_process_days 处理请求时距离首次下载天数
 * @property int|null $original_record_id App数据库原始退款请求记录ID
 * @property int|null $notification_id 推送通知记录ID
 * @property string $the_day 推送通知日期
 * @property string|null $last_push_time 最后推送通知时间
 * @property string|null $environment 环境
 * @property string|null $app_bundle_id App包名
 * @property string|null $app_account_token App Account Token
 * @property string|null $product_id 商品ID
 * @property string|null $original_transaction_id 原始购买订单号
 * @property string|null $first_original_transaction_id 订单号
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 */
class CohortRefundRequest extends BaseModel
{
    protected $connection = 'cohort';

    protected $table = 'refund_requests';

    protected $guarded = [];
}
