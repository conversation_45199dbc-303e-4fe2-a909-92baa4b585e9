<?php

namespace App\Console\Commands\Cohort;

use App\Console\Commands\LegacyBaseCommand;
use App\Libs\Apple\AppStoreConnect;
use App\Libs\Utils;
use App\Models\App;
use App\Models\AppleDeveloper;
use App\Models\Cohort\CohortAppConfig;
use App\Models\Cohort\CohortRefundPolicy;
use App\Models\Cohort\CohortRefundRequest;
use Carbon\Carbon;

/**
 * 处理苹果退款请求并回传消费信息
 *
 * 功能说明：
 * 1. 定时处理待处理的退款请求，向苹果回传用户消费信息以影响退款决策
 * 2. 根据配置的退款策略计算消费信息参数，支持硬编码和RP比例控制两种模式
 * 3. 记录处理结果和时间统计，便于分析退款处理效率和结果
 *
 * 主要处理逻辑：
 * - 筛选已启用退款请求功能的App中未处理的退款请求
 * - 跳过超过3天的退款请求（苹果API限制）
 * - 调用退款策略计算消费信息（如账号年龄、使用时长、消费状态等）
 * - 通过App Store Connect API向苹果回传消费信息
 * - 更新退款请求记录，包括处理结果、响应状态码、时间统计等
 * - 如果订单已退款，更新退款结果相关字段
 *
 * 退款策略模式：
 * - hardcoded模式：使用预定义的策略逻辑（ID 1-6）
 * - rp_control模式：按配置的比例随机选择refundPreference值，并应用固定参数覆盖
 */
class CohortProcessRefundRequests extends LegacyBaseCommand
{
    protected $signature = 'cohort-process-refund-requests';

    protected $isSupervisorProcess = true;

    protected $maxExecutionMinutes = 60;
    protected $logItemProcessAvgSpeed = true;

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->onCommandStart();

        $refundRequestEnabledAdamIds = CohortAppConfig::where('refund_request_enabled', 1)->pluck('adam_id')->toArray();
        $this->logInfo("refundRequestEnabledAdamIds: " . implode(',', $refundRequestEnabledAdamIds));

        $refundRequests = CohortRefundRequest::whereIn('adam_id', $refundRequestEnabledAdamIds)
            ->whereNull('process_time')
            ->whereRaw("sched_process_time < UTC_TIMESTAMP()")
            ->orderBy('sched_process_time')
            ->get();
        if ($refundRequests->isEmpty()) {
            $this->logInfo('No refund request to process');
            $this->onCommandFinish();
            return;
        }

        $n = count($refundRequests);
        $this->logInfo("$n refund requests to process");
        foreach ($refundRequests as $i => $refundRequest) {
            $ii = $i + 1;
            $this->logInfo("$ii/$n Process refund request #{$refundRequest->id} {$refundRequest->app_name} {$refundRequest->transaction_id}");
            $now = now()->utc();
            $delayHours = Utils::cleanNumber($now->diffInMinutes($refundRequest->first_push_time) / 60, 1);

            // 检查退款请求是否超过3天，如果超过则跳过处理
            $daysSinceFirstPush = $now->diffInDays($refundRequest->first_push_time);
            if ($daysSinceFirstPush > 3) {
                $this->logInfo("跳过处理：退款请求已超过3天 (距离首次推送 {$daysSinceFirstPush} 天)");
                $refundRequest->update([
                    'process_time' => $now,
                    'process_delay_hours' => $delayHours,
                    'process_result' => '超时跳过',
                    'process_response_code' => 501,
                    'process_response_body' => "退款请求已超过3天限制，距离首次推送 {$daysSinceFirstPush} 天",
                ]);
                continue;
            }

            $refundRequest->update([
                'process_time' => $now,
                'process_delay_hours' => $delayHours,
            ]);
            try {
                $refundInfo = CohortRefundPolicy::computeRefundInfo($refundRequest->adam_id, $refundRequest->transaction_id, $refundRequest->app_account_token, $refundRequest->refund_policy_id);
                $this->logInfo(Utils::stringify($refundInfo, false));
                $orderInfo = $refundInfo['orderInfo'];
                $consumptionInfo = $refundInfo['consumptionInfo'];
                if (!empty($consumptionInfo['downloadTime'])) {
                    $downloadTime = new Carbon($consumptionInfo['downloadTime'], 'UTC');
                    $downloadToRefundRequestHours = Utils::cleanNumber($downloadTime->diffInMinutes($refundRequest->first_push_time) / 60, 1);
                } else {
                    $downloadTime = null;
                    $downloadToRefundRequestHours = null;
                }
                $refundRequest->update([
                    'region_id' => $orderInfo['regionId'],
                    'sku_display_name' => $orderInfo['skuDisplayName'],
                    'sku_price' => $orderInfo['skuPrice'],
                    'refund_success_time' => $orderInfo['refundSuccessTime'],
                    'download_time' => $downloadTime,
                    'download_to_refund_request_hours' => $downloadToRefundRequestHours,
                    'download_to_process_days' => $consumptionInfo['diffInDays'],
                    'download_to_process_hours' => $consumptionInfo['diffInHours'],
                    'download_to_process_minutes' => $consumptionInfo['diffInMinutes'],
                ]);
                if (!empty($orderInfo['refundSuccessTime'])) {
                    $refundRequest->update([
                        'refund_result' => '已退款',
                        'refund_result_track_time' => $now,
                        'refund_result_track_hours' => Utils::cleanNumber($now->diffInMinutes($refundRequest->first_push_time) / 60, 1),
                        'refund_success_hours' => $now->diffInHours($refundRequest->first_push_time),
                    ]);
                }
                $refundRequest->update([
                    'account_tenure' => $consumptionInfo['accountTenure'],
                    'consumption_status' => $consumptionInfo['consumptionStatus'],
                    'customer_consented' => $consumptionInfo['customerConsented'],
                    'delivery_status' => $consumptionInfo['deliveryStatus'],
                    'lifetime_dollars_purchased' => $consumptionInfo['lifetimeDollarsPurchased'],
                    'lifetime_dollars_refunded' => $consumptionInfo['lifetimeDollarsRefunded'],
                    'platform' => $consumptionInfo['platform'],
                    'play_time' => $consumptionInfo['playTime'],
                    'refund_preference' => $consumptionInfo['refundPreference'],
                    'sample_content_provided' => $consumptionInfo['sampleContentProvided'],
                    'user_status' => $consumptionInfo['userStatus'],
                ]);
                $app = App::of($refundRequest->app_id);
                $developer = AppleDeveloper::of($app->developer_id);
                $asc = new AppStoreConnect($developer);
                list($statusCode, $resData, $resBody) = $asc->sendConsumptionInformation($refundRequest->app_bundle_id, $refundRequest->transaction_id, $consumptionInfo);

                // 判断状态码是否为2xx，只有2xx才算成功
                if ($statusCode >= 200 && $statusCode < 300) {
                    $this->logInfo("回传成功: $statusCode $resBody");
                    $refundRequest->update([
                        'process_result' => '回传成功',
                        'process_response_code' => $statusCode,
                        'process_response_body' => $resBody,
                    ]);
                } else {
                    $this->logInfo("回传失败: $statusCode $resBody");
                    $refundRequest->update([
                        'process_result' => '回传失败',
                        'process_response_code' => $statusCode,
                        'process_response_body' => $resBody,
                    ]);
                }
            } catch (\Exception $e) {
                $this->logException($e, "Failed to send refund consumption info for refund request #{$refundRequest->id}");
                $refundRequest->update([
                    'process_result' => 'Exception',
                    'process_response_code' => 999,
                    'process_response_body' => (string)$e,
                ]);
            }
        }

        $this->onCommandFinish();
    }

}
