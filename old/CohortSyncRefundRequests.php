<?php

namespace App\Console\Commands\Cohort;

use App\Console\Commands\LegacyBaseCommand;
use App\Libs\Utils;
use App\Models\Cohort\CohortAppConfig;
use App\Models\Cohort\CohortOrder;
use App\Models\Cohort\CohortRefundRequest;
use App\Models\Sys\SysConfig;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

/**
 * 同步苹果退款请求数据到数据分析库
 *
 * 功能说明：
 * 1. 从各App数据库的apple_server_consumption_requests表同步退款请求到统一的分析库
 * 2. 为每个退款请求分配处理策略和计划处理时间
 * 3. 关联订单信息，获取SKU、价格、地区等数据
 * 4. 从推送通知中提取退款原因
 *
 * 主要处理逻辑：
 * - 根据App名称或Adam ID筛选需要同步的App配置
 * - 过滤非生产环境和不匹配的Adam ID数据
 * - 为新的退款请求创建记录，包含原始数据和计算字段
 * - 随机分配处理延迟时间（如0-48小时），避免集中处理
 * - 根据App配置或系统默认值分配退款策略ID
 * - 查询关联订单获取商品信息和购买时间
 * - 从apple_server_notifications表提取退款原因
 * - 更新已存在记录的推送次数和最后推送时间
 *
 * 配置参数：
 * - refund_request.sched_process_delay_hours: 处理延迟时间选项数组
 * - refund_request.default_refund_policy_id: 默认退款策略ID选项数组
 *
 * 退款策略分配：
 * - 优先使用App配置的refund_policy_id（支持逗号分隔的多个ID随机选择）
 * - 如未配置则使用系统默认策略ID
 *
 * 与退款处理流程的关系：
 * - CohortSyncRefundRequests: 同步退款请求数据（本类）
 * - CohortProcessRefundRequests: 处理退款请求并回传消费信息
 * - CohortTrackProcessedRefundRequests: 跟踪最终退款结果
 */
class CohortSyncRefundRequests extends LegacyBaseCommand
{
    protected $signature = 'cohort-sync-refund-requests {app_name_or_adam_id}';

    protected $maxExecutionMinutes = 60;
    protected $logItemProcessAvgSpeed = true;

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->onCommandStart();

        /**
         * refund_request.default_refund_policy_id set 9
         * refund_request.sched_process_delay_hours set 0
         * refund_request.track_interval_minutes int 60
         */

        // 0
        $schedProcessDelayHourOptions = SysConfig::loadValue('refund_request.sched_process_delay_hours');
        $this->logInfo("schedProcessDelayHourOptions: {$this->stringify($schedProcessDelayHourOptions)}");

        // 9
        $defaultRefundPolicyIdOptions = SysConfig::loadValue('refund_request.default_refund_policy_id');
        $this->logInfo("defaultRefundPolicyIdOptions: {$this->stringify($defaultRefundPolicyIdOptions)}");

        $appNameOrAdamId = $this->argument('app_name_or_adam_id');
        $this->logInfo("appNameOrAdamId: $appNameOrAdamId");

        $appConfigs = CohortAppConfig::where(function($query) use ($appNameOrAdamId) {
            $query->where('app_name', $appNameOrAdamId)
                ->orWhere('adam_id', $appNameOrAdamId);
        })->where('refund_request_supported', 1)
            ->get();
        $this->logInfo(count($appConfigs) . ' apps to sync');
        // 每个应用循环
        foreach ($appConfigs as $appConfig) {
            $this->logInfo("Process app #{$appConfig->id} {$appConfig->app_name} {$appConfig->adam_id}");

            // 清理不应当处理的记录
            DB::connection($appConfig->db_connection)
                ->table("{$appConfig->db_name}.apple_server_consumption_requests")
                ->whereNull('oa_sync_time')
                ->where(function ($query) use ($appConfig) {
                    $query->where('adam_id', '<>', $appConfig->adam_id);
                    $query->orWhere('environment', '<>', 'production');
                })
                ->update(['oa_sync_time' => new Carbon(null, 'UTC')]);

            // oa_sync_time: 未处理 
            $rows = DB::connection($appConfig->db_connection)
                ->table("{$appConfig->db_name}.apple_server_consumption_requests")
                ->whereNull('oa_sync_time')
                ->get();
            $this->logInfo(count($rows) . ' rimage.pngows to sync');
            foreach ($rows as $row) {
                $refundRequest = CohortRefundRequest::where('app_id', $appConfig->id)
                    ->where('original_record_id', $row->id)
                    ->first();
                if (empty($refundRequest)) {
                    $schedProcessDelayHours = (int)Arr::random($schedProcessDelayHourOptions);
                    $now = now()->utc();
                    $attrs = (array)$row;
                    $firstPushTime = new Carbon($attrs['first_push_time'], 'UTC');
                    $attrs['app_id'] = $appConfig->id;
                    $attrs['developer_id'] = $appConfig->developer_id;
                    $attrs['app_name'] = $appConfig->app_name;
                    $attrs['original_record_id'] = $attrs['id'];
                    $attrs['oa_sync_time'] = (string)$now;
                    $attrs['sched_process_delay_hours'] = $schedProcessDelayHours;
                    $attrs['sched_process_time'] = $firstPushTime->copy()->addHours($schedProcessDelayHours);
                    unset($attrs['id'], $attrs['created_at'], $attrs['updated_at']);
                    if (!empty($appConfig->refund_policy_id)) {
                        $refundPolicyId = max(1, Arr::random(explode(',', $appConfig->refund_policy_id)));
                    } else {
                        $refundPolicyId = (int)Arr::random($defaultRefundPolicyIdOptions);
                    }
                    $attrs['refund_policy_id'] = $refundPolicyId;

                    // 获取退款原因
                    if (!empty($row->notification_id)) {
                        $notification = DB::connection($appConfig->db_connection)
                            ->table("{$appConfig->db_name}.apple_server_notifications")
                            ->where('id', $row->notification_id)
                            ->first();
                        if ($notification && $notification->decoded_content) {
                            $decodedContent = json_decode($notification->decoded_content, true);
                            if (!empty($decodedContent['data']['consumptionRequestReason'])) {
                                $attrs['refund_reason'] = $decodedContent['data']['consumptionRequestReason'];
                            }
                        }
                    }

                    $refundRequest = CohortRefundRequest::create($attrs);
                    $order = CohortOrder::forApp($appConfig->adam_id)
                        ->where('transaction_id', $refundRequest->transaction_id)
                        ->first();
                    if ($order) {
                        $orderTime = new Carbon($order->order_time, 'UTC');
                        $orderToRefundRequestHours = Utils::cleanNumber($orderTime->diffInMinutes($refundRequest->first_push_time) / 60, 1);
                        $refundRequest->update([
                            'region_id' => $order->region_id,
                            'sku_display_name' => $order->sku_display_name,
                            'sku_price' => $order->price,
                            'order_time' => $orderTime,
                            'order_to_refund_request_hours' => $orderToRefundRequestHours,
                        ]);
                    }
                } else {
                    $refundRequest->update(Arr::only((array)$row, ['last_push_time', 'push_cnt']));
                }
                if (empty($row->oa_sync_time)) {
                    DB::connection($appConfig->db_connection)
                        ->table("{$appConfig->db_name}.apple_server_consumption_requests")
                        ->where('id', $row->id)
                        ->update(['oa_sync_time' => $refundRequest->oa_sync_time]);
                }
            }
        }

        $this->onCommandFinish();
    }

}
