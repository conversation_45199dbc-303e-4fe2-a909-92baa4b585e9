<?php

namespace App\Models\Cohort;

use App\Models\BaseModel;

/**
 * Cohort应用配置模型
 *
 * 管理每个App的数据库连接、处理状态和统一表同步配置
 *
 * 数据表：app_config (连接：cohort)
 *
 * === 基本信息字段 ===
 * @property int $id 主键ID
 * @property string|null $app_name 应用名称
 * @property int|null $developer_id 开发者ID
 * @property int|null $category_id 品类ID
 * @property int|null $adam_id App Store应用ID，全局唯一标识
 * @property string|null $bundle_id 应用包名
 * @property string|null $alt_bundle_id 备用包名
 *
 * === 数据库配置字段 ===
 * @property string|null $db_connection 数据库连接名称
 * @property string|null $db_name 数据库名称
 * @property string|null $db_struct_version 数据库结构版本（V2020/V2021）
 * @property string|null $exp_struct_version 实验组结构版本
 *
 * === 退款请求配置字段 ===
 * @property bool $refund_request_supported 是否支持退款请求
 * @property bool $refund_request_enabled 是否启用退款请求
 * @property string|null $refund_policy_id 退款策略ID
 * @property bool $apple_notifications_sync_enabled 是否启用苹果通知同步
 * @property int|null $apple_notifications_last_sync_id 最后同步的通知ID（断点续传）
 *
 * === 任务调度配置字段 ===
 * @property bool $order_scheduled_tasks_enabled 是否启用订单定时任务
 * @property bool $metrics_scheduled_tasks_enabled 是否启用指标定时任务
 *
 * === 数据处理进度字段 ===
 * @property int $max_processed_attribution_record_id 最大已处理归因记录ID
 * @property string|null $max_processed_order_date 最大已处理订单日期
 * @property int $max_processed_device_experiment_group_id 最大已处理设备实验组ID
 * @property \Carbon\Carbon|null $attribution_processed_at 归因数据最后处理时间
 * @property \Carbon\Carbon|null $order_processed_at 订单数据最后处理时间
 * @property \Carbon\Carbon|null $device_experiment_group_processed_at 设备实验组最后处理时间
 *
 * === 指标处理进度字段 ===
 * @property string|null $max_processed_metrics_daily_date 最大已处理日指标日期
 * @property string|null $max_processed_metrics_weekly_date 最大已处理周指标日期
 * @property string|null $max_processed_metrics_monthly_date 最大已处理月指标日期
 * @property \Carbon\Carbon|null $metrics_daily_processed_at 日指标最后处理时间
 * @property \Carbon\Carbon|null $metrics_weekly_processed_at 周指标最后处理时间
 * @property \Carbon\Carbon|null $metrics_monthly_processed_at 月指标最后处理时间
 *
 * === 统一同步控制字段 ===
 * @property bool $unified_sync_enabled 是否启用统一表同步
 * @property int $unified_sync_priority 同步优先级，数值越大优先级越高
 *
 * === 订单表同步字段 ===
 * @property \Carbon\Carbon|null $orders_last_sync_time 订单表最后同步时间
 * @property int $orders_last_sync_count 订单表最后同步记录数
 * @property string $orders_sync_status 订单表同步状态：pending/syncing/completed/failed
 * @property string|null $orders_sync_error_message 订单表同步错误信息
 * @property string|null $orders_sync_lock_value 订单表同步锁标识
 * @property \Carbon\Carbon|null $orders_sync_started_at 订单表同步开始时间
 * @property \Carbon\Carbon|null $orders_sync_completed_at 订单表同步完成时间
 *
 * === 订单账户表同步字段 ===
 * @property \Carbon\Carbon|null $order_accounts_last_sync_time 订单账户表最后同步时间
 * @property int $order_accounts_last_sync_count 订单账户表最后同步记录数
 * @property string $order_accounts_sync_status 订单账户表同步状态：pending/syncing/completed/failed
 * @property string|null $order_accounts_sync_error_message 订单账户表同步错误信息
 * @property string|null $order_accounts_sync_lock_value 订单账户表同步锁标识
 * @property \Carbon\Carbon|null $order_accounts_sync_started_at 订单账户表同步开始时间
 * @property \Carbon\Carbon|null $order_accounts_sync_completed_at 订单账户表同步完成时间
 *
 * === 实验组表同步字段 ===
 * @property \Carbon\Carbon|null $experiment_groups_last_sync_time 实验组表最后同步时间
 * @property int $experiment_groups_last_sync_count 实验组表最后同步记录数
 * @property string $experiment_groups_sync_status 实验组表同步状态：pending/syncing/completed/failed
 * @property string|null $experiment_groups_sync_error_message 实验组表同步错误信息
 * @property string|null $experiment_groups_sync_lock_value 实验组表同步锁标识
 * @property \Carbon\Carbon|null $experiment_groups_sync_started_at 实验组表同步开始时间
 * @property \Carbon\Carbon|null $experiment_groups_sync_completed_at 实验组表同步完成时间
 *
 * === 设备实验组表同步字段 ===
 * @property \Carbon\Carbon|null $device_experiment_group_last_sync_time 设备实验组表最后同步时间
 * @property int $device_experiment_group_last_sync_count 设备实验组表最后同步记录数
 * @property string $device_experiment_group_sync_status 设备实验组表同步状态：pending/syncing/completed/failed
 * @property string|null $device_experiment_group_sync_error_message 设备实验组表同步错误信息
 * @property string|null $device_experiment_group_sync_lock_value 设备实验组表同步锁标识
 * @property \Carbon\Carbon|null $device_experiment_group_sync_started_at 设备实验组表同步开始时间
 * @property \Carbon\Carbon|null $device_experiment_group_sync_completed_at 设备实验组表同步完成时间
 *
 * === 设备归因表同步字段 ===
 * @property \Carbon\Carbon|null $device_attributions_last_sync_time 设备归因表最后同步时间
 * @property int $device_attributions_last_sync_count 设备归因表最后同步记录数
 * @property string $device_attributions_sync_status 设备归因表同步状态：pending/syncing/completed/failed
 * @property string|null $device_attributions_sync_error_message 设备归因表同步错误信息
 * @property string|null $device_attributions_sync_lock_value 设备归因表同步锁标识
 * @property \Carbon\Carbon|null $device_attributions_sync_started_at 设备归因表同步开始时间
 * @property \Carbon\Carbon|null $device_attributions_sync_completed_at 设备归因表同步完成时间
 *
 * === 全局同步信息字段 ===
 * @property string|null $sync_details 同步详情信息（JSON格式）
 * @property \Carbon\Carbon|null $sync_updated_at 同步状态最后更新时间
 *
 * === 时间戳字段 ===
 * @property \Carbon\Carbon|null $created_at 创建时间
 * @property \Carbon\Carbon|null $updated_at 更新时间
 *
 * === 业务规则 ===
 * - adam_id为全局唯一标识，用于区分不同App
 * - unified_sync_enabled控制是否参与统一表同步
 * - 各表同步状态独立管理，支持并行同步
 * - 同步锁机制防止多进程冲突
 * - 错误信息记录便于故障排查
 *
 * === 使用示例 ===
 * ```php
 * // 获取指定App的配置
 * $config = CohortAppConfig::of(1549825498);
 *
 * // 启用统一同步
 * $config->update(['unified_sync_enabled' => 1]);
 *
 * // 检查订单表同步状态
 * if ($config->orders_sync_status === 'completed') {
 *     // 同步已完成
 * }
 *
 * // 获取所有启用同步的App
 * $enabledApps = CohortAppConfig::where('unified_sync_enabled', 1)->get();
 * ```
 */
class CohortAppConfig extends BaseModel
{
    protected $connection = 'cohort';

    protected $table = 'app_config';

    protected $guarded = [];

    /**
     * 需要转换为日期的字段
     */
    protected $dates = [
        'attribution_processed_at',
        'order_processed_at',
        'device_experiment_group_processed_at',
        'metrics_daily_processed_at',
        'metrics_weekly_processed_at',
        'metrics_monthly_processed_at',
        'orders_last_sync_time',
        'orders_sync_started_at',
        'orders_sync_completed_at',
        'order_accounts_last_sync_time',
        'order_accounts_sync_started_at',
        'order_accounts_sync_completed_at',
        'experiment_groups_last_sync_time',
        'experiment_groups_sync_started_at',
        'experiment_groups_sync_completed_at',
        'device_experiment_group_last_sync_time',
        'device_experiment_group_sync_started_at',
        'device_experiment_group_sync_completed_at',
        'device_attributions_last_sync_time',
        'device_attributions_sync_started_at',
        'device_attributions_sync_completed_at',
        'sync_updated_at',
        'created_at',
        'updated_at'
    ];

    /**
     * 获取支持的表类型
     */
    public static function getSupportedTableTypes(): array
    {
        return config('cohort.sync.supported_table_types');
    }

    /**
     * 获取同步状态常量
     */
    public static function getSyncStatusConstants(): array
    {
        return config('cohort.sync.status');
    }

    /**
     * 根据adam_id获取配置
     */
    public static function of($adamId)
    {
        return self::where('adam_id', $adamId)->first();
    }

    /**
     * 获取所有启用统一同步的App配置
     */
    public static function getUnifiedSyncEnabled()
    {
        return self::where('unified_sync_enabled', 1)
            ->orderBy('unified_sync_priority', 'desc')
            ->orderBy('adam_id')
            ->get();
    }

    /**
     * 检查指定表是否需要同步
     */
    public function needsSync($tableType)
    {
        if (!in_array($tableType, self::getSupportedTableTypes())) {
            return false;
        }

        if (!$this->unified_sync_enabled) {
            return false;
        }

        $status = $this->{"{$tableType}_sync_status"};
        $statusConstants = self::getSyncStatusConstants();
        return in_array($status, [$statusConstants['pending'], $statusConstants['failed']]);
    }

    /**
     * 获取指定表的同步状态
     */
    public function getSyncStatus($tableType)
    {
        if (!in_array($tableType, self::getSupportedTableTypes())) {
            return null;
        }

        return [
            'status' => $this->{"{$tableType}_sync_status"},
            'last_sync_time' => $this->{"{$tableType}_last_sync_time"},
            'last_sync_count' => $this->{"{$tableType}_last_sync_count"},
            'error_message' => $this->{"{$tableType}_sync_error_message"},
            'started_at' => $this->{"{$tableType}_sync_started_at"},
            'completed_at' => $this->{"{$tableType}_sync_completed_at"}
        ];
    }

    /**
     * 更新指定表的同步状态
     */
    public function updateSyncStatus($tableType, $status, $options = [])
    {
        if (!in_array($tableType, self::getSupportedTableTypes())) {
            return false;
        }

        $updateData = [
            "{$tableType}_sync_status" => $status,
            'sync_updated_at' => now()
        ];

        $statusConstants = self::getSyncStatusConstants();

        if ($status === $statusConstants['syncing']) {
            $updateData["{$tableType}_sync_started_at"] = now();
            $updateData["{$tableType}_sync_error_message"] = null;

            if (isset($options['lock_value'])) {
                $updateData["{$tableType}_sync_lock_value"] = $options['lock_value'];
            }
        }

        if ($status === $statusConstants['completed']) {
            $updateData["{$tableType}_sync_completed_at"] = now();
            $updateData["{$tableType}_sync_error_message"] = null;
            $updateData["{$tableType}_sync_lock_value"] = null;

            if (isset($options['sync_count'])) {
                $updateData["{$tableType}_last_sync_count"] = $options['sync_count'];
            }

            if (isset($options['sync_time'])) {
                $updateData["{$tableType}_last_sync_time"] = $options['sync_time'];
            } else {
                $updateData["{$tableType}_last_sync_time"] = now();
            }
        }

        if ($status === $statusConstants['failed']) {
            $updateData["{$tableType}_sync_completed_at"] = now();
            $updateData["{$tableType}_sync_lock_value"] = null;

            if (isset($options['error_message'])) {
                $updateData["{$tableType}_sync_error_message"] = $options['error_message'];
            }
        }

        return $this->update($updateData);
    }

    /**
     * 检查是否可以获取同步锁
     */
    public function canAcquireSyncLock($tableType, $timeoutMinutes = 30)
    {
        if (!in_array($tableType, self::getSupportedTableTypes())) {
            return false;
        }

        $status = $this->{"{$tableType}_sync_status"};
        $startedAt = $this->{"{$tableType}_sync_started_at"};

        // 如果不是同步中状态，可以获取锁
        $statusConstants = self::getSyncStatusConstants();
        if ($status !== $statusConstants['syncing']) {
            return true;
        }

        // 如果同步中但超时，可以获取锁
        if ($startedAt && $startedAt->addMinutes($timeoutMinutes)->isPast()) {
            return true;
        }

        return false;
    }
}
