<?php

namespace App\Console\Commands\Cohort;

use App\Console\Commands\LegacyBaseCommand;
use App\Libs\Utils;
use App\Models\Cohort\CohortAppConfig;
use App\Models\Cohort\CohortRefundRequest;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

/**
 * 同步苹果推送通知数据，更新退款请求状态
 *
 * 功能说明：
 * 1. 从各App数据库的apple_server_notifications表同步REFUND/REFUND_DECLINED通知
 * 2. 根据通知类型更新退款请求的最终状态
 * 3. 支持增量同步和历史数据回填两种模式
 * 4. 保护已有的有意义的退款结果，避免数据覆盖
 *
 * 主要处理逻辑：
 * - 检查App是否支持苹果通知同步
 * - 简单检查apple_server_notifications表是否存在
 * - 分批处理通知（每批1000条），避免内存溢出
 * - 统一处理模式：总是从last_sync_id+1开始处理所有后续通知
 * - 终态保护：已退款状态永远不会被覆盖，命令重新运行总是安全
 * - 跳过非退款相关通知，但仍算作已处理（断点续传）
 * - REFUND通知：立即设置为"已退款(收到退款通知)"（终态）
 * - REFUND_DECLINED通知：记录拒绝时间，继续跟踪（非终态，仅2.0版本）
 * - 支持limit参数：限制本次处理数量，便于调试测试
 *
 * 版本兼容性：
 * - 2.0版本：从decoded_content提取数据，支持REFUND/REFUND_DECLINED，环境为Production
 * - 1.0版本：从request_content提取数据，仅支持REFUND，环境为PROD
 * - 通过transaction_id匹配退款请求，支持多种数据路径的容错解析
 *
 * 使用示例：
 * - 正常同步：php artisan cohort-sync-apple-notifications app_name
 * - 调试模式：php artisan cohort-sync-apple-notifications app_name --limit=10
 * - 历史回填：设置last_sync_id为null后运行正常同步命令
 *
 * 与退款处理流程的关系：
 * - CohortSyncRefundRequests: 同步退款请求数据
 * - CohortSyncAppleNotifications: 同步苹果通知数据（本类）
 * - CohortTrackRefundRequests: 跟踪最终退款结果
 */
class CohortSyncAppleNotifications extends LegacyBaseCommand
{
    protected $signature = 'cohort-sync-apple-notifications
                            {app_name_or_adam_id}
                            {--limit= : 本次最多处理多少条通知，用于调试测试}';

    protected $maxExecutionMinutes = 60;
    protected $logItemProcessAvgSpeed = true;

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->onCommandStart();

        $appNameOrAdamId = $this->argument('app_name_or_adam_id');
        $limit = $this->option('limit');

        // 参数验证和日志
        if ($limit !== null) {
            $limit = (int)$limit;
            if ($limit <= 0) {
                $this->error('limit参数必须是正整数');
                return;
            }
            $this->logInfo("appNameOrAdamId: $appNameOrAdamId, limit: {$limit} (调试模式)");
        } else {
            $this->logInfo("appNameOrAdamId: $appNameOrAdamId (生产模式)");
        }

        $appConfigs = CohortAppConfig::where(function($query) use ($appNameOrAdamId) {
            $query->where('app_name', $appNameOrAdamId)
                ->orWhere('adam_id', $appNameOrAdamId);
        })->where('apple_notifications_sync_enabled', 1)
            ->get();

        $this->logInfo(count($appConfigs) . ' apps to sync apple notifications');

        foreach ($appConfigs as $appConfig) {
            $this->logInfo("Process app #{$appConfig->id} {$appConfig->app_name} {$appConfig->adam_id}");

            // 简单检查表是否存在
            if (!$this->checkTableExists($appConfig)) {
                continue;
            }

            // 统一处理逻辑
            $this->processNotificationsFromId($appConfig, $limit);
        }

        $this->onCommandFinish();
    }

    /**
     * 检查apple_server_notifications表是否存在
     */
    private function checkTableExists($appConfig)
    {
        try {
            DB::connection($appConfig->db_connection)
                ->table("{$appConfig->db_name}.apple_server_notifications")
                ->limit(1)
                ->get();
            return true;
        } catch (\Exception $e) {
            $this->logInfo("App #{$appConfig->id} {$appConfig->app_name}: apple_server_notifications表不存在或无法访问，跳过");
            return false;
        }
    }

    /**
     * 统一处理通知：从last_sync_id+1开始处理所有后续通知
     */
    private function processNotificationsFromId($appConfig, $limit = null)
    {
        $lastSyncId = $appConfig->apple_notifications_last_sync_id ?? 0;

        // 获取待处理总数
        $totalCount = $this->getTotalNotificationsCount($appConfig, $lastSyncId, $limit);

        if ($totalCount === 0) {
            $this->logInfo("从通知ID {$lastSyncId} 开始处理：没有待处理的通知");
            return;
        }

        $this->logInfo("从通知ID {$lastSyncId} 开始处理：共 {$totalCount} 条待处理通知" . ($limit ? "（限制: {$limit} 条）" : ""));

        $batchSize = 1000;
        $totalProcessed = 0;
        $currentId = $lastSyncId;

        do {
            // 动态调整批次大小
            $currentBatchSize = $limit ? min($batchSize, $limit - $totalProcessed) : $batchSize;

            if ($currentBatchSize <= 0) {
                $this->logInfo("已达到限制数量 {$limit}，停止处理");
                break;
            }

            $notifications = DB::connection($appConfig->db_connection)
                ->table("{$appConfig->db_name}.apple_server_notifications")
                ->where('id', '>', $currentId)
                ->orderBy('id')
                ->limit($currentBatchSize)
                ->get();

            if ($notifications->isEmpty()) {
                break;
            }

            $progressPercent = $totalCount > 0 ? round(($totalProcessed / $totalCount) * 100, 1) : 0;
            $this->logInfo("处理批次: " . count($notifications) . " 条通知 (ID: {$notifications->first()->id} - {$notifications->last()->id}) [进度: {$totalProcessed}/{$totalCount} ({$progressPercent}%)]");

            foreach ($notifications as $notification) {
                $this->processNotificationSafely($notification, $appConfig);
                $totalProcessed++;

                // 检查是否达到限制
                if ($limit && $totalProcessed >= $limit) {
                    $this->logInfo("已处理 {$totalProcessed} 条通知，达到限制数量");
                    $currentId = $notification->id; // 更新当前ID
                    break 2; // 跳出两层循环
                }
            }

            $currentId = $notifications->last()->id;

            // 实时更新断点
            $appConfig->update(['apple_notifications_last_sync_id' => $currentId]);

        } while (count($notifications) === $currentBatchSize);

        $finalPercent = $totalCount > 0 ? round(($totalProcessed / $totalCount) * 100, 1) : 0;
        $this->logInfo("本次共处理 {$totalProcessed}/{$totalCount} 条通知 ({$finalPercent}%)" . ($limit ? "（限制: {$limit}）" : "") . "，最新ID: {$currentId}");
    }

    /**
     * 获取待处理通知总数
     */
    private function getTotalNotificationsCount($appConfig, $lastSyncId, $limit = null)
    {
        $query = DB::connection($appConfig->db_connection)
            ->table("{$appConfig->db_name}.apple_server_notifications")
            ->where('id', '>', $lastSyncId);

        $totalCount = $query->count();

        // 如果有限制，返回较小值
        return $limit ? min($totalCount, $limit) : $totalCount;
    }



    /**
     * 判断是否为退款相关通知
     */
    private function isRefundRelatedNotification($notification)
    {
        $notificationType = $notification->notification_type;

        // 检查是否为生产环境
        if (!$this->isProductionEnvironment($notification)) {
            return false;
        }

        // 2.0版本支持 REFUND 和 REFUND_DECLINED
        if ($notification->version === '2.0') {
            return in_array($notificationType, ['REFUND', 'REFUND_DECLINED']);
        }

        // 1.0版本只支持 REFUND
        return $notificationType === 'REFUND';
    }

    /**
     * 判断是否为生产环境
     */
    private function isProductionEnvironment($notification)
    {
        $environment = $notification->environment;

        // 2.0版本：Production
        // 1.0版本：PROD
        return in_array($environment, ['Production', 'PROD']);
    }

    /**
     * 安全处理单个通知（统一逻辑，包含终态保护）
     */
    private function processNotificationSafely($notification, $appConfig)
    {
        // 检查通知类型，只处理退款相关通知
        if (!$this->isRefundRelatedNotification($notification)) {
            // 跳过非退款相关通知，但仍算作已处理
            return;
        }

        $this->logInfo("Process notification #{$notification->id} {$notification->notification_type} (version: " . ($notification->version ?: '1.0') . ")");

        $transactionId = $this->extractTransactionId($notification);
        if (!$transactionId) {
            $this->logInfo("无法提取transaction_id，跳过");
            return;
        }

        $refundRequest = CohortRefundRequest::where('adam_id', $appConfig->adam_id)
            ->where('transaction_id', $transactionId)
            ->first();

        if (!$refundRequest) {
            $this->logInfo("未找到 {$appConfig->app_name} 订单 $transactionId 对应的退款请求，跳过");
            return;
        }

        // 终态保护：已退款状态永远不会被覆盖
        if ($this->isTerminalState($refundRequest)) {
            $this->logInfo("退款请求 #{$refundRequest->id} 已是终态（{$refundRequest->refund_result}），跳过");
            return;
        }

        $this->processNotificationForRequest($notification, $refundRequest);
    }

    /**
     * 判断是否为终态（已退款状态）
     */
    private function isTerminalState($refundRequest)
    {
        return in_array($refundRequest->refund_result, [
            '已退款(订单轮询)',
            '已退款(收到退款通知)'
        ]);
    }

    /**
     * 为特定退款请求处理通知
     */
    private function processNotificationForRequest($notification, $refundRequest)
    {
        if ($notification->notification_type === 'REFUND') {
            $this->handleRefundNotification($notification, $refundRequest);
        } elseif ($notification->notification_type === 'REFUND_DECLINED') {
            $this->handleRefundDeclinedNotification($notification, $refundRequest);
        }
    }

    /**
     * 处理REFUND通知（退款成功）
     */
    private function handleRefundNotification($notification, $refundRequest)
    {
        $refundTime = $this->extractRefundTime($notification);
        $this->logInfo("处理REFUND通知，设置为已退款状态");

        $refundRequest->update([
            'refund_result' => '已退款(收到退款通知)',
            'refund_success_time' => $refundTime,
            'refund_success_hours' => Utils::cleanNumber($refundTime->diffInMinutes($refundRequest->first_push_time) / 60, 1),
        ]);
    }

    /**
     * 处理REFUND_DECLINED通知（退款被拒绝）
     */
    private function handleRefundDeclinedNotification($notification, $refundRequest)
    {
        $declineTime = $this->extractDeclineTime($notification);
        $this->logInfo("处理REFUND_DECLINED通知，记录拒绝时间");

        $updates = [
            'last_refund_decline_time' => $declineTime,
        ];

        if (empty($refundRequest->first_refund_decline_time)) {
            $updates['first_refund_decline_time'] = $declineTime;
        }

        $refundRequest->update($updates);
        // 注意：不更新refund_result，让跟踪逻辑处理状态显示
    }

    /**
     * 从通知中提取transaction_id
     */
    private function extractTransactionId($notification)
    {
        // 2.0版本：从decoded_content提取
        if ($notification->version === '2.0' && !empty($notification->decoded_content)) {
            $decoded = json_decode($notification->decoded_content, true);
            return $decoded['data']['transaction']['transactionId']
                ?? $decoded['transaction']['transactionId'] ?? null;
        }

        // 1.0版本：从request_content提取
        if (!empty($notification->request_content)) {
            $requestData = json_decode($notification->request_content, true);
            if (!empty($requestData['unified_receipt']['latest_receipt_info'])) {
                $latestInfo = $requestData['unified_receipt']['latest_receipt_info'][0] ?? null;
                return $latestInfo['transaction_id'] ?? $latestInfo['original_transaction_id'] ?? null;
            }
            return $requestData['original_transaction_id'] ?? null;
        }

        // 兜底：使用表字段
        return $notification->original_transaction_id ?? null;
    }

    /**
     * 从REFUND通知中提取退款时间
     */
    private function extractRefundTime($notification)
    {
        // 2.0版本：从decoded_content的revocationDate提取
        if ($notification->version === '2.0' && !empty($notification->decoded_content)) {
            $decoded = json_decode($notification->decoded_content, true);
            $timestamp = $decoded['data']['transaction']['revocationDate']
                ?? $decoded['transaction']['revocationDate'] ?? null;

            if ($timestamp) {
                return Carbon::createFromTimestampMs($timestamp);
            }
        }

        // 1.0版本：从request_content的cancellation_date提取
        if (!empty($notification->request_content)) {
            $requestData = json_decode($notification->request_content, true);
            if (!empty($requestData['unified_receipt']['latest_receipt_info'])) {
                $latestInfo = $requestData['unified_receipt']['latest_receipt_info'][0] ?? null;
                $cancellationDate = $latestInfo['cancellation_date'] ?? null;

                if ($cancellationDate) {
                    return new Carbon($cancellationDate);
                }
            }
        }

        // 兜底：使用通知接收时间
        return $notification->receive_date ? new Carbon($notification->receive_date) : now();
    }

    /**
     * 从REFUND_DECLINED通知中提取拒绝时间
     */
    private function extractDeclineTime($notification)
    {
        return $notification->receive_date ? new Carbon($notification->receive_date) : now();
    }
}
