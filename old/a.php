<?php
/**
 * root@iZt4n3i2g243bce16yfcpvZ:/etc/supervisor/conf.d# cat cohort-process-refund-requests.conf 
 * [program:cohort-process-refund-requests]
 * command=php /root/www/zy.dev/artisan cohort-process-refund-requests
 * autostart=true
 * autorestart=true
 * user=www-data
 * redirect_stderr=true
 * stdout_logfile=/root/logs/cohort-process-refund-requests.log
 * stdout_logfile_maxbytes=0
 */


// 获取启用了退款请求的应用
$adamIds = CohortAppConfig::where('refund_request_enabled', 1)->pluck('adam_id');
foreach ($adamIds as $adamId) {
    $schedule->command("cohort-sync-refund-requests $adamId")
        ->everyFiveMinutes()
        ->withoutOverlapping(30)
        ->runInBackground()
        ->appendOutputTo(storage_path("logs/cohort-sync-refund-requests-$adamId.log"));
}

// 开启基于苹果推送通知的退款结果同步
$adamIds = CohortAppConfig::where('apple_notifications_sync_enabled', 1)->pluck('adam_id');
foreach ($adamIds as $adamId) {
    $schedule->command("cohort-sync-apple-notifications $adamId")
        ->everyTenMinutes()
        ->withoutOverlapping(60)
        ->runInBackground()
        ->appendOutputTo(storage_path("logs/cohort-sync-apple-notifications-$adamId.log"));
}